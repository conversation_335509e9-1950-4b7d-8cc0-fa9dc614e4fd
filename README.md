# 
jeeplus-parent/jeeplus-ord/lawcase-admin

cd /Users/<USER>/Desktop/ww/saas20250729/jeeplus-parent/jeeplus-ord/lawcase-admin
mvn install
mvn spring-boot:run

http://localhost:7500

# 修改一下 /Users/<USER>/Desktop/ww/saas20250729/jeeplus-parent/jeeplus-ord/lawcase-admin/src/main/resources/logback.xml
    <!-- 日志存放路径 -->
	<property name="log.path" value="/Users/<USER>/ruoyi/logs" />

# /Users/<USER>/Desktop/ww/saas20250729/jeeplus-parent/admin-vue
node 20
npm install -g pnpm
pnpm install
pnpm dev
http://localhost:8081/index


# /Users/<USER>/Desktop/ww/saas20250729/jeeplus-parent/user-admin-vue
node 16
pnpm i
pnpm serve
做了包版本降级 冲突

# /Users/<USER>/Desktop/ww/saas20250729/jeeplus-parent/jeeplus
mvn install
cd /Users/<USER>/Desktop/ww/saas20250729/jeeplus-parent/jeeplus/jeeplus-web
mvn spring-boot:run