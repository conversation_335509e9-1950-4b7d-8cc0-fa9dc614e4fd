server:
  port: 8081
  servlet:
    context-path: /law
  tomcat:
    uri-encoding: UTF-8
    basedir: /Users/<USER>
    max-threads: 2000
    min-spare-threads: 10
    accesslog:
      pattern: common
      enabled: true
      directory: logs
      prefix: jeeplus_access_log
      suffix: .log
      request-attributes-enabled: true
      rename-on-rotate: true
    max-http-form-post-size: 200MB
  jetty:
    max-http-form-post-size: 20000000B
logging:
  level:
    # DEBUG INFO ERROR
    root: INFO
    #    org.flowable: DEBUG
    com.jeeplus: ERROR
    org.apache.shiro.cache.ehcache.EhCacheManager: ERROR
spring:
  servlet:
    multipart:
      maxFileSize:  1000MB
      maxRequestSize: 1000MB
  devtools:
    restart:
      enabled: true
  #  profiles:
  #    active: dev
  #autoconfigure:
    #exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  jpa:
    open-in-view: false
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        show_sql: false
        format_sql: false
    database-platform: org.hibernate.dialect.MySQL8Dialect
  datasource:
    username: root
    password: root
    url: ******************************************************************************************************************************************************************************************************************
    driverClassName: com.mysql.cj.jdbc.Driver
    initialSize: 5
    minIdle: 5
    maxActive: 20
    maxWait: 60000
    # 连接池配置优化
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    validationQuery: SELECT 1
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    # 减少连接超时警告
    removeAbandoned: false
 


  # if you need use pg database , please enable this config.
  #  jpa:
  #    properties:
  #      hibernate:
  #        jdbc:
  #          lob:
  #            non_contextual_creation: true
  #spring-ehcache的配置
  cache:
    type: ehcache # redis/ehcache， 在这里设置你使用的缓存框架，如果不想使用redis，请改成ehcache
  ehcache:
    config: classpath:ehcache.xml
  redis:
    host: localhost
    port: 6379
    expireTime: 3600000 #单位秒
  mvc.servlet.load-on-startup: 1
  jmx:
    enabled: false
  # quartz任务配置
  quartz:
    job-store-type: jdbc
    properties:
      org:
        quartz:
          scheduler:
            instanceName: clusteredScheduler
            instanceId: AUTO
          jobStore:
            selectWithLockSQL: SELECT * FROM {0}LOCKS UPDLOCK WHERE LOCK_NAME = ?
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            tablePrefix: QRTZ_
            isClustered: false # 打开集群配置
            clusterCheckinInterval: 2000 # 设置集群检查间隔20s
            useProperties: false
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
flowable:
  # 关闭定时任务Job
  async-executor-activate: true
  check-process-definitions: false
  process-definition-location-prefix: classpath:/processes/
  database-schema-update: false
  common:
    app:
      idm-url: http://localhost:9999
      idm-admin:
        user: admin
        password: test
#mybatis的配置
 
#是否开启 swagger，生产环境请关闭
swagger:
  enable: true
#cas服务端的地址
cas:
  server-url-prefix: https://www.cainiao.com:8443/cas
#oss配置
config:
  accessory:
    type: minIO       #local, aliyun, minIO
    baseDir: file
    local:
      location: d:\\accessory
    aliyun:
      endpoint: oss-cn-hangzhou.aliyuncs.com
      accessKeyId: LTAI4Fhraue2ayc1KQMkZz5F
      accessKeySecret: ******************************
      bucketName: testajgw
    minIO:
      endpoint:
      accessKey:
      secretKey:
      bucketName:
#tx配置
wps:
  appid: SX20230418KRPLWR
  appsecret: iKAYrGgoNOeTrvSTagwtdTObxKMGIxla
  download_host: https://saas1.lgy0999.com/law
  domain: https://wwo.wps.cn
  downloadCallbackPath: /www/law_project/server-data/wps
  webctx: /law
#============================#
#===== System settings ======#
#============================#
# 域名
domain.name: https://saas1.lgy0999.com/law

#产品信息设置
copyrightYear: 2019
version: springboot2.0
#演示模式: 不能操作和保存的模块： sys
demoMode: false
#上传文件绝对路径, 路径中不允许包含“userfiles”
userfiles:
  basedir: /www/law_project/server-data/      # 文件上传路径，可以留空
  allowedType: file  # 允许上传的文件类型， all, file ,image, audio, video, office
  extensions:
    all: all       # 允许上传所有类型文件
    file: 7z,aiff,asf,avi,bmp,csv,doc,docx,fla,flv,gif,gz,gzip,jpeg,jpg,mid,mov,mp3,mp4,mpc,mpeg,mpg,ods,odt,pdf,png,ppt,pptx,pxd,qt,ram,rar,rm,rmi,rmvb,rtf,sdc,sitd,swf,sxc,sxw,tar,tgz,tif,tiff,txt,vsd,wav,wma,wmv,xls,xlsx,zip,apk        # 只允许上传安全文件（linux系统非可执行）
    image: gif,jpg,jpeg,bmp,png     # 只允许上传图片
    audio: CD,OGG,MP3,ASF,WMA,WAV,MP3PRO,RM,REAL,APE,MODULE,MIDI,VQF    # 只允许上传音频
    video: AVI,WMV,RM,RMVB,MPEG1,MPEG2,MPEG4(MP4),3GP,ASF,SWF,VOB,DAT,MOV,M4V,FLV,F4V,MKV,MTS,TS     # 只允许上传视频
    office: txt,xls,xlsx,xlsm,xltx,xltm,xlsb,xlam,doc,docx,docm,dotx,dotm,ppt,pptx,pptm,ppsx,ppsm,potx,potm,ppam     # 只允许上传office文件
#JWT access token过期时间（access过期时间，并不是指需要重新登录的超时时间，而是指需要刷新重新获取access token的时间，超时登录的时间是3*EXPIRE_TIME，即refresh token的有效时时间)
jwt.accessToken.expireTime: 86400000 # 24小时，单位毫秒


#===============================#
#====== license 信息 ================#
#===============================#

# 产品授权
productId:
license:

socketIo:
  host: 127.0.0.1
  prot: 11004
  # 设置最大每帧处理数据的长度，防止他人利用大数据来攻击服务器
  maxFramePayloadLength: 1048576
  # 设置http交互最大内容长度
  maxHttpContentLength: 1048576
  # 协议升级超时时间（毫秒），默认10秒。HTTP握手升级为ws协议超时时间
  upgradeTimeout: 25000
  # Ping消息超时时间（毫秒），默认60秒，这个时间间隔内没有接收到心跳消息就会发送超时事件
  pingTimeout: 120000
  # Ping消息间隔（毫秒），默认25秒。客户端向服务器发送一条心跳消息间隔
  pingInterval: 300000
  # socket连接数大小（如只监听一个端口boss线程组为1即可）
  bossCount: 1
  workCount: 100
  allowCustomRequests: true

#=======================================================================#
#===== plugin设置 =====#
#Plugin插件会读取该core和admin包下的基础类，如果修改了路径或者类名，请同步修改这里
#=======================================================================#
plugin:
  Page: com.jeeplus.core.persistence.Page
  User: com.jeeplus.modules.sys.entity.User
  Area: com.jeeplus.modules.sys.entity.Area
  Office: com.jeeplus.modules.sys.entity.Office
  UserUtils: com.jeeplus.modules.sys.utils.UserUtils
