import axios from 'axios';
import { Message } from 'element-ui';
import { getCookie } from "./util";

// 环境的切换
//  axios.defaults.baseURL = 'https://saas.lgy0999.com/api';
 axios.defaults.baseURL = 'http://127.0.0.1:7500/api';

// 请求超时时间
axios.defaults.timeout = 30000;

// post请求头
axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8';
 
axios.defaults.headers.common.token = getCookie("token");

/**
 * get方法，对应get请求
 * @param {String} url [请求的url地址]
 * @param {Object} params [请求时携带的参数]
 */
export function get(url, params){
  return new Promise((resolve, reject) =>{
 
    axios.get(url, {
      params: params,
	  headers: { 'token': getCookie('token')}
    })
      .then(res => {
        if(res.data.code != 0){
          Message.warning(
            {
              showClose: true,
              message: res.data.msg,
              type: 'warning'
            }
          )
        }
        resolve(res.data);
      })
      .catch(err => {
        reject(err.data)
      })
  });
}

/**
 * post方法，对应post请求
 * @param {String} url [请求的url地址]
 * @param {Object} params [请求时携带的参数]
 */
export function post(url, params) {
  return new Promise((resolve, reject) => {
    axios.post(url, params,{ headers: { 'token': getCookie('token')}})
      .then(res => {
        if(res.data.code != 0){
          Message.warning(
            {
              showClose: true,
              message: res.data.msg,
              type: 'warning'
            }
          )
        }
        resolve(res.data);
      })
      .catch(err => {
        reject(err.data)
      })
  });
}
