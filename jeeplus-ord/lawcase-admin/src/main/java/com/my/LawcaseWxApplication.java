package com.my;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.web.client.RestTemplate;

/**
 * 启动程序
 * 
 * <AUTHOR>
 */
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
public class LawcaseWxApplication
{
	   @Autowired
	    private RestTemplateBuilder builder;


	    @Bean
	    public RestTemplate getRestTemplate(){
	        return builder.build();
	    }
  
    public static void main(String[] args)
    {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(LawcaseWxApplication.class, args);
        System.out.println("律管云管理系统启动成功");
        // 打印端口
        System.out.println("http://localhost:7500");
    }
}
