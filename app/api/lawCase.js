let lawCase = (vm)=>{
	return {
		// //注册用户
		// registered(params={}){
		// 	params['telephone'] = getApp().globalData.mobile
		// 	return vm.$u.post('/api/oauth/carrierMobile/registered'+vm.$u.queryParams(params))
		// }
		//案件列表
		list(data){
			return vm.$u.post('/law/app/lawcase/case/list',data)
		},
		//案件程序列表
		typeTreeData(type){
			return vm.$u.post('/law/lawcase/caseProgram/typeTreeData',{type})
		},
		//案由
		treeData(){
			return vm.$u.post('/law/lawcase/caseCause/treeData')
		},
		//主办人员
		allBriefList(){
			return vm.$u.post('/law/sys/user/allBriefList')
		},
		//新增案件
		saveInfo(data){
			console.log(data,'提交')
			return vm.$u.post('/law/lawcase/case/saveInfo',data)
		},
		//编辑案件
		editCase(data){
			return vm.$u.post('/law/lawcase/case/save',data)
		},
		//删除案件
		delCase(ids){
			return vm.$u.post('/law/lawcase/case/delete',{ids})
		},
		//案件详情
		detailInfo(id){
			return vm.$u.post('/law/lawcase/case/detailInfo',{id})
		},
		//添加当事人
		addLitigant(data){
			return vm.$u.post('/law/case/caseConcernPerson/save',data)
		},
		//删除当事人
		deleteLitigant(ids){
			return vm.$u.post('/law/case/caseConcernPerson/delete',{ids})
		},
		//当事人详情
		LitigantInfo(id){
			return vm.$u.post('/law/case/caseConcernPerson/queryById',{id})
		},
		//所属行业
		industryData(){
			return vm.$u.get('/law/lawcase/industry/treeData')
		},
		//客户列表
		customerList(data){
			return vm.$u.post('/law/lawcase/customer/list',data)
		},
		//客户详情
		customerInfo(id){
			return vm.$u.post('/law/lawcase/customer/queryById',{id})
		},
		//新增客户
		addCustomer(data){
			return vm.$u.post('/law/app/lawcase/customer/save',data)
		},
		//删除客户
		deleteCustomer(ids){
			return vm.$u.post('/law/lawcase/customer/delete',{ids})
		},
		//编辑客户
		editCustomer(data){
			return vm.$u.post('/law/lawcase/customer/save',data)
		},
		//关联列表
		caseRelationList(id){
			return vm.$u.post('/law/case/caseRelation/list',{'lawCase.id':id})
		},
		//新增关联
		addCaseRelation(data){
			return vm.$u.post('/law/case/caseRelation/save',data)
		},
		//解除关联
		deleteCaseRelation(ids){
			return vm.$u.post('/law/case/caseRelation/delete',{ids})
		},
		//案件信息(案件关联)
		caseRelationInfoList(caseId){
			return vm.$u.post('/law/case/caseRelation/caseList',{caseId})
		},
		//办案策略列表
		caseHandleStrategyList(id){
			return vm.$u.post('/law/case/caseHandleStrategy/list',{'lawCase.id':id})
		},
		//新增策略
		addCaseHandleStrategy(data){
			return vm.$u.post('/law/case/caseHandleStrategy/save',data)
		},
		//查询策略
		getCaseHandleStrategy(id){
			return vm.$u.post('/law/case/caseHandleStrategy/queryById',{id})
		},
		//受理情况
		saveAccept(data){
			return vm.$u.post('/law/app/lawcase/case/saveAccept',data)
		},
		//承办人列表
		caseUndertakePerson(id){
			return vm.$u.post('/law/case/caseUndertakePerson/list',{
				'lawCase.id':id
			})
		},
		//新增，编辑承办人
		saveCaseUndertakePerson(data){
			return vm.$u.post('/law/case/caseUndertakePerson/save',data)
		},
		//承办人详情
		caseUndertakePersonInfo(id){
			return vm.$u.post('/law/case/caseUndertakePerson/queryById',{id})
		},
		//删除承办人
		delCaseUndertakePerson(ids){
			return vm.$u.post('/law/case/caseUndertakePerson/delete',{ids})
		},
		//财产保全列表
		casePropertyPreservationList(id){
			return vm.$u.post('/law/case/casePropertyPreservation/list',{
				'lawCase.id':id
			})
		},
		//财产保全详情
		casePropertyPreservationInfo(id){
			return vm.$u.post('/law/case/casePropertyPreservation/queryById',{id})
		},
		//财产保全(添加、编辑)
		saveCasePropertyPreservation(data){
			return vm.$u.post('/law/case/casePropertyPreservation/save',data)
		},
		//财产保全(删除)
		delCasePropertyPreservation(ids){
			return vm.$u.post('/law/case/casePropertyPreservation/delete',{ids})
		},
		
		//庭审记录列表
		caseTrialRecordList(id){
			return vm.$u.post('/law/case/caseTrialRecord/list',{
				'lawCase.id':id
			})
		},
		//庭审记录详情
		caseTrialRecordInfo(id){
			return vm.$u.post('/law/case/caseTrialRecord/queryById',{id})
		},
		//庭审记录(添加、编辑)
		saveCaseTrialRecord(data){
			return vm.$u.post('/law/case/caseTrialRecord/save',data)
		},
		//庭审记录(删除)
		delCaseTrialRecord(ids){
			return vm.$u.post('/law/case/caseTrialRecord/delete',{ids})
		},
		
		//执行情况列表
		caseExecuteSituationList(id){
			return vm.$u.post('/law/case/caseExecuteSituation/list',{
				'lawCase.id':id
			})
		},
		//执行情况详情
		caseExecuteSituationInfo(id){
			return vm.$u.post('/law/case/caseExecuteSituation/queryById',{id})
		},
		//执行情况(添加、编辑)
		saveCaseExecuteSituation(data){
			return vm.$u.post('/law/case/caseExecuteSituation/save',data)
		},
		//执行情况(删除)
		delCaseExecuteSituation(ids){
			return vm.$u.post('/law/case/caseExecuteSituation/delete',{ids})
		},
		
		//新增阶段
		saveCaseStage(data){
			return vm.$u.post('law/lawcase/caseStage/save',data)
		},
		//阶段列表
		caseStageList(id){
			return vm.$u.post('/law/lawcase/caseStage/list',{
				'lawCase.id':id
			})
		},
		//设为当前阶段
		caseStageSetCurrent(id){
			return vm.$u.post('/law/lawcase/caseStage/setCurrent',{id})
		},
		//删除阶段
		delCaseStage(ids){
			return vm.$u.post('/law/lawcase/caseStage/delete',{ids})
		},
		//新增记录
		saveTodoInfo(data){
			return vm.$u.post('/law/lawcase/todoInfo/save',data)
		},
		//记录详情
		TodoInfo(id){
			return vm.$u.post('/law/lawcase/todoInfo/queryById',{id})
		},
		
		//案件文档
		fileData(lawCaseId='',directoryId=''){
			return vm.$u.get('/law/app/lawcase/case/fileData',{
				lawCaseId,
				directoryId,
			})
		},
		//创建目录
		caseFileDirectory(data){
			return vm.$u.post('/law/lawcase/caseFileDirectory/save',data)
		},
		//删除目录
		delCaseFileDirectory(ids){
			return vm.$u.post('/law/lawcase/caseFileDirectory/delete',{ids})
		},
		//删除文件
		delCaseFile(ids){
			return vm.$u.post('/law/lawcase/caseFile/delete',{ids})
		},
		//审核案件
		auditCase(data){
			return vm.$u.post('/law/app/lawcase/case/audit',data)
		},
		//删除记录
		delTodoInfo(ids){
			return vm.$u.post('/law/lawcase/todoInfo/delete',{ids})
		},
		//更改待办状态
		todoInfoStatus(data){
			return vm.$u.post('/law/lawcase/todoInfo/changeStatus',data)
		},
		//财务流水
		listByCase(id){
			return vm.$u.post('/law/lawcase/financeFlowRecord/listByCase',{
				'lawCase.id':id
			})
		},
		//新增收入
		flowRecordsaveInfo(data){
			return vm.$u.post('/law/lawcase/financeFlowRecord/saveInfo',data)
		},
		//新增支出
		financeFlowRecordSave(data){
			return vm.$u.post('/law/lawcase/financeFlowRecord/save',data)
		},
	}
}

export default lawCase