let common = (vm)=>{
	return {
		// //注册用户
		// registered(params={}){
		// 	params['telephone'] = getApp().globalData.mobile
		// 	return vm.$u.post('/api/oauth/carrierMobile/registered'+vm.$u.queryParams(params))
		// }
		//短信验证码登录
		login(data){
			return vm.$u.post('/law/app/sys/login',data)
		},
		//短信验证码登录
		logout(){
			return vm.$u.get('/law/app/sys/logout')
		},
		//个人信息
		userInfo(){
			return vm.$u.get('/law/app/sys/user/info')
		},
		//修改个人信息
		saveInfo(data){
			return vm.$u.post('/law/sys/user/saveInfo',data)
		},
		//获取字典
		getDictMap(){
			return vm.$u.get('/law/app/sys/dict/getDictMap')
		},
		//修改密码
		savePwd(data){
			return vm.$u.post('/law/sys/user/savePwd',data)
		},
	}
}

export default common