<template>
	<scroll-view class="view_list" refresher-enabled="true" :refresher-triggered="triggered" @refresherrefresh='refresh'
		 :scroll-y="true" @scrolltolower="scroll">
		 <slot></slot>
	</scroll-view>
</template>

<script>
	export default {
		name:"MyList",
		props: {
		    // 配置信息
		    option: {
		        type: Object,
		        default: () => ({})
		    }
		},
		data() {
			return {
				defaultOption:{
					triggered: false,
					pageNo: 1,
					pageSize: 15,
					isLoadMore: false,
				},
				currentPage: 1,
				currentSize: 15,
				triggered: false,
				isLoadMore: false,
			};
		},
		mounted() {
			this.load()
		},
		methods: {
			refresh() {
				this.defaultOption = Object.assign(this.defaultOption, this.option);
				this.currentPage = this.defaultOption.pageNo;
				this.currentSize = this.defaultOption.pageSize;
				// this.list = []
				this.page = 1
				this.pageSize = 15
				this.isLoadMore = false
				this.triggered = true;
				let paging = { pageNo: this.currentPage, pageSize: this.currentSize };
				this.$emit('refresh',paging)
			},
			scroll() {
				console.log(this.isLoadMore,111111111111)
				if (!this.isLoadMore) { //此处判断，上锁，防止重复请求
					this.isLoadMore = true
					this.currentPage += 1
					this.load()
				}
			},
			load() {
				let paging = { pageNo: this.currentPage, pageSize: this.currentSize };
				this.$emit('load',paging)
			},
			loadSuccess(data = {}){
				const { list, total } = data;
				console.log(data,'6666666')
				if (list.length > total) {
					this.isLoadMore = true
				} else {
					this.isLoadMore = false
				}
				this.triggered = false;
			},
			refreshSuccess(data = {}){
				const { list, total } = data;
				this.isLoadMore = false
				this.triggered = true;
			}
		}
	}
</script>

<style lang="scss">
.view_list {
			flex: 1;
			overflow-y: auto;
		}
</style>
