<template>
	<view class="form_box" v-if="form">
		<view class="u-flex-1" style="overflow-y: auto;box-sizing: border-box;">
			<u-form :label-width='230' :model="form" ref="uForm" :error-type="['toast']" :border-bottom='false'>
				<block v-for="(item,key) in formConfig" :key='key'>
					<u-cell-group :title='item.title' :border="false">
						<block v-for="(i,k) in item.form" :key='k'>
							<u-cell-item title="" :arrow="false" hover-class='none'>
								<u-form-item :label="i.label" :prop="i.name" :border-bottom='false'>
									<block v-if="i.type=='input'">
										<u-input :type="i.config.type" :clearable='false'  v-model="form[i.name]" :placeholder="i.rules[0].message"/>
										{{i.config.rightText}}
									</block>
									<block v-if="i.type=='select'">
										<u-input type="select" :select-open="status[i.name]" v-model="form[i.name]" :placeholder="i.rules[0].message" @click="status[i.name] = true"></u-input>
										<u-select 
										:label-name="i.config.label" 
										:value-name="i.config.value" 
										v-model="status[i.name]" 
										:list="i.list" 
										:mode="i.config.mode"
										@confirm="confirm($event,i.name)"></u-select>
									</block>
									<block v-if="i.type=='picker'">
										<u-input type="select" :select-open="status[i.name]" v-model="form[i.name]" :placeholder="i.rules[0].message" @click="status[i.name] = true"></u-input>
										<u-picker @confirm="pickConfirm($event,i.name)" v-model="status[i.name]"  :mode="i.config.mode" :params="i.config.params"></u-picker>
									</block>
								</u-form-item>
							</u-cell-item>
						</block>
						<slot></slot>
						<view v-show="false">
							{{item.key}}
						</view>
						<!--  -->
					</u-cell-group>
					<view class="u-flex u-row-right" v-if="item.add">
						<view class="u-text-right" style="margin: 10px 30px 0 0;"  @click="add">
							<u-icon name="plus" size="28"></u-icon>
							添加当事人
						</view>
					</view>
				</block>
			</u-form>
		</view>
		<view class="u-padding-30">
			<u-button type="primary" @click="submit">提交</u-button>
		</view>
	</view>
</template>

<script>
	import creatForm from '@/components/creatForm.vue'
	export default {
		components: {
			creatForm
		},
		props: {
			formConfig: {
				type: Array,
				default: () => []
			}
		},
		data() {
			return {
				form: {},
				rules: {},
				status:{},
				list:[],
			};
		},
		created() {
			
		},
		watch:{
			formConfig:{
				handler(n,o){
					this.setRule()
					console.log('触发了')
				},
				deep:true,
			},
			 
		},
		mounted() {
			this.setRule()
			// this.formConfig.map(item => {
			// 	item.form.map(i => {
			// 		let key = `${i.name}`
			// 		if(i.type=='select'){
			// 			this.status = {[key]:false,...this.status}
			// 		}
			// 		// this.form[key] = i.value
			// 		this.rules[key] = i.rules
			// 	})
			// })
			// this.$refs.uForm.setRules(this.rules);
		},
		methods: {
			setRule(){
				this.formConfig.map(item => {
					item.form.map(i => {
						let key = `${i.name}`
						if(['select','picker'].includes(i.type)){
							this.status = {[key]:false,...this.status}
						}
						if(!this.form[i.name]){
							this.form[i.name] = ''
						}
						// if(Object.values(this.form).every(item=>item==true)){
							console.log('jinru')
							// this.form[key] = String(i.value||'')
						// }
						this.rules[key] = i.rules
					})
				})
				this.$refs.uForm.setRules(this.rules);
				console.log(this.form,'**********')
			},
			add(){
				this.$emit('add')
			},
			confirm(item,name) {
				this.form[name] = item[item.length-1].label
				let result = {
					name,
					...item[item.length-1]
				}
				this.$emit('confirm',result)
			},
			pickConfirm(item,name) {
				console.log(item['hour'])
				
				if(item['hour']){
					this.form[name] = `${item['year']}-${item['month']}-${item['day']} ${item['hour']}:${item['minute']}:${item['second']}`
				}else{
					this.form[name] = `${item['year']}-${item['month']}-${item['day']}`
				}
			},
			submit() {
				console.log(this.form,'-------------');
				// console.log(this.rules);
				this.$refs.uForm.validate(valid => {
					if (valid) {
						this.$emit('submit',this.form)
						console.log('验证成功')
					} else {
						console.log('验证失败');
					}
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	.form_box {
		display: flex;
		flex-direction: column;
		height: 100%;

		/deep/ .u-cell {
			padding: 0 20px;
		}
	}
</style>
