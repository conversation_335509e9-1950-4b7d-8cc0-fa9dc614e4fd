<template>
	<view class="calendar-box">
		<view class="month">
			<view @click="lastMonth">上月</view>
			<view>{{year}}年{{month}}月</view>
			<view @click="nextMonth">下月</view>
			<picker v-if="checkDate" class="picker" mode="date" fields="month" @change="changeDate" />
		</view>
		<view class="week">
			<view :style="'color:'+(weeks==weeked?bgweek:'')+';'" v-for="weeks in weekArr">{{weeks}}</view>
		</view>
		<view class="day u-flex-1">
			<view class="dayItem"  :style="{height:height||'',background:(days.date==localDate?bgday:'')}" :class="[{'checkday':days.date==''},{'choose':days.date==localDate},{'check':days.flag}]"
				 v-for="(days,index) in dayArr" :key="index"
				@click="signToday(days,index)">
				{{days.day}}
				<view>
					<view class="u-line-1 u-margin-bottom-10 dayList" v-for="(i,k) in days.list" >{{i.name}}</view>
					
				</view>
				<!-- <view :class="[{'circle':days.flag},{'repair':days.day<day},{'sign':days.day==day}]"></view> -->
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			lang: {
				type: String,
				default: 'zh'
			},
			type: {
				type: String,
				default: 'calendar'
			},
			checkDate: {
				type: Boolean,
				default: false
			},
			bgweek: {
				type: String,
				default: '#FF8F22'
			},
			bgday: {
				type: String,
				default: '#FF8F22'
			}
		},
		data() {
			return {
				height:'',
				weeked: '', // 今天周几
				dayArr: [], // 当前月每日
				localDate: '', // 今天日期
				day: new Date().getDate(), // 当前日
				year: new Date().getFullYear(), // 当前年
				month: new Date().getMonth() + 1, // 当前月
				weekArr: ['日', '一', '二', '三', '四', '五', '六'], // 每周
			}
		},
		mounted() {
			let that = this;
			// 初始日期
			that.initDate();
			// 今天日期
			that.localDate = that.year + '-' + that.formatNum(that.month) + '-' + that.formatNum(that.day);
			// 中英切换
			if (that.lang != 'zh') that.weekArr = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
			// 今天周几
			that.weeked = that.weekArr[new Date().getDay()];
			
			// 签到功能
			// if (that.type != 'calendar') {
			// 	for (let i in that.dayArr) {
			// 		that.$set(that.dayArr[i], 'flag', false);
			// 		that.$set(that.dayArr[i], 'list', []);
			// 	}
			// }
		},
		methods: {
			// 选择年月
			changeDate(e) {
				let that = this;
				that.year = parseInt(e.detail.value.split('-')[0]);
				that.month = parseInt(e.detail.value.split('-')[1]);
				that.initDate();
			},
			// 点击签到
			signToday(obj, index) {
				this.$emit('change', obj.date);
				if(obj.date){
					this.dayArr.map(item=>{
						item['flag'] = false
					})
					this.dayArr[index]['flag'] = true					
				}
				return
				let that = this;
				// 指定签到类型可访问
				if (that.type == 'calendar') return;
				// 限制本月可进行签到
				if ((new Date().getMonth() + 1) != parseInt(obj.date.split('-')[1])) return;
				// 禁用非本月点击签到且限制签到日期小于等于本日
				if (obj.date != '' && obj.day <= that.day) {
					// 开启已签到提醒
					if (that.dayArr[index].flag) {
						uni.showToast({
							title: '已签到',
							icon: 'none'
						});
					} else {
						uni.showToast({
							title: that.day > obj.day ? '补签成功' : '签到成功',
							icon: 'success'
						});
						that.$set(that.dayArr[index], 'flag', true);
						that.$emit('change', obj.date);
					}
				}
			},
			// 初始化日期
			initDate() {
				let that = this;
				that.dayArr = [];
				// 当前月总天数
				let totalDay = new Date(that.year, that.month, 0).getDate();
				// 遍历总天数将日期逐个添加至数组
				for (let i = 1; i <= totalDay; i++) {
					// 得到需补充天数
					let value = (new Date(that.year, that.month - 1, i)).getDay();
					// 补充前面空白日期
					if (i == 1 && value != 0) that.addBefore(value);
					// 添加本月日期
					let obj = {};
					obj.date = that.year + '-' + that.formatNum(that.month) + '-' + that.formatNum(i);
					obj.day = i;
					that.dayArr.push(obj);
					// 补充后面空白日期
					if (i == totalDay && value != 6) that.addAfter(value);
				}
				if (that.type != 'calendar') {
					for (let i in that.dayArr) {
						that.$set(that.dayArr[i], 'flag', false);
						that.$set(that.dayArr[i], 'list', []);
					}
				}
				this.height = ''
				setTimeout(()=>{
					const query = uni.createSelectorQuery().in(this);
					            query.select('.dayItem').boundingClientRect(data => {
									this.height = data.height*2 + 'rpx'
					            }).exec();
				},100)
				console.log(totalDay,'*********')
				this.$emit('changeMonth',`${this.year}-${this.month}`)
			},
			// 补充前面空白日期
			addBefore(value) {
				let that = this;
				let totalDay = new Date(that.year, that.month - 1, 0).getDate();
				for (let i = 0; i < value; i++) {
					let obj = {};
					obj.date = '';
					obj.day = totalDay - (value - i) + 1;
					that.dayArr.push(obj);
				}
			},
			// 补充后空白日期
			addAfter(value) {
				let that = this;
				for (let i = 0; i < (6 - value); i++) {
					let obj = {};
					obj.date = '';
					obj.day = i + 1;
					that.dayArr.push(obj);
				}
			},
			// 格式化日期位数
			formatNum(num) {
				return num < 10 ? ('0' + num) : num;
			},
			// 上一个月
			lastMonth() {
				let that = this;
				if (that.month == 1) {
					that.year -= 1;
					that.month = 12;
				} else {
					that.month -= 1;
				}
				that.initDate();
			},
			// 下一个月
			nextMonth() {
				let that = this;
				if (that.month == 12) {
					that.year += 1;
					that.month = 1;
				} else {
					that.month += 1;
				}
				that.initDate();
			}
		}
	}
</script>

<style scoped lang="scss">
	.calendar-box {
		width: 100%;
		height: 100%;
		flex-direction: column;
		justify-content: center;
	}
.dayList{
	font-size: 16rpx;
	color: #FFFFFF;
	background:$u-type-primary-disabled;
	.view{
		border-radius: 5px;
	}
}
	.calendar-box,
	.month,
	.week {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	.month{
		padding: 0 30rpx;
	}
	.week{
		padding-bottom: 30rpx;
	}
.day {
		display: flex;
		justify-content: space-between;
	}
	.month,
	.week,
	.day {
		// width: 700rpx;
	}

	.month {
		margin: 30rpx 0;
		position: relative;
	}
	.check{
		background-color: #ccc;
	}
	.picker {
		width: 160rpx;
		height: 40rpx;
		position: absolute;
		top: 20rpx;
		left: 50%;
		transform: translate(-50%, -50%);
	}

	.day {
		flex-wrap: wrap;
		border-top: 1rpx solid #f5f5f5;
		border-left: 1rpx solid #f5f5f5;
	}

	.week>view,
	.day>view {
		width: 14%;
		// width: 100rpx;
		// height: 100rpx;
		text-align: center;
		position: relative;
		/* line-height: 100rpx; */
	}

	.checkday {
		color: #999;
	}

	.choose {
		color: #FFFFFF;
		/* border-radius: 50%; */
	}

	.circle {
		width: 10rpx;
		height: 10rpx;
		border-radius: 50%;
		position: absolute;
		bottom: 10%;
		left: 50%;
		transform: translate(-50%, -50%);
	}

	.sign {
		background-color: #0089fe;
	}

	.repair {
		background-color: #f4a01a;
	}
	.dayItem{
		// height: 200rpx!important;
		/* background-color: #18B566; */
		border-right: 1rpx solid #f5f5f5;
		border-bottom: 1rpx solid #f5f5f5;
		text-align: left!important;
		overflow: hidden;
	}
</style>
