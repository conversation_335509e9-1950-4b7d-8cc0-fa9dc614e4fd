export default {
	data(){
		return{
			triggered: false,
			page: 1,
			pageSize: 15,
			isLoadMore: false,
		}
	},
	onPullDownRefresh: function() {
		Object.assign(this, this.$options.data());
		this.getList();
	},
	methods:{
		init(){
			this.list = []
			this.page = 1
			this.pageSize = 15
			this.isLoadMore = false
			// Object.assign(this, this.$options.data());
			this.triggered = true;
			this.getList().then(res=>{
				this.triggered = false;
			})
		},
		scroll() {
			if (!this.isLoadMore) { //此处判断，上锁，防止重复请求
				this.isLoadMore = true
				this.page += 1
				this.getList()
			}
		},
	}
}
