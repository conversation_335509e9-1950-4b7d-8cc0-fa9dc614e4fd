<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
			
			// plus.push.createMessage( '消息显示的内容', "消息承载的数据");
			plus.push.addEventListener("click",function(message){
				let payload = message.payload;
				uni.switchTab({
					url:'/pages/calendar/calendar'
				})
				try{
					
				}catch(e){
					//TODO handle the exception
				}
			})
			plus.push.addEventListener("receive",function(message){
				let payload = message.payload;
				try{
					
				}catch(e){
					//TODO handle the exception
				}
			})
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style lang="scss">
	@import "uview-ui/index.scss";
	@import './styles/global.scss';
	page{
		height: 100%;
		background-color: #f5f5f5;
		padding: 0;
		margin: 0;
		// padding-top:  var(--status-bar-height);
		box-sizing:border-box;
	}
	/*每个页面公共css */
</style>
