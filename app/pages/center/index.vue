<template>
	<view>
		<u-navbar :is-back="false" title="　" :border-bottom="false">
			<view class="u-flex u-row-right" style="width: 100%;">
				<view class="camera u-flex u-row-center">
					<!-- <u-icon name="camera-fill" color="#000000" size="48"></u-icon> -->
				</view>
			</view>
		</u-navbar>
		<view class="u-flex user-box u-p-l-30 u-p-r-20 u-p-b-30">
			<view class="u-m-r-10">
				<u-avatar :src="pic" size="140"></u-avatar>
			</view>
			<view class="u-flex-1">
				<view class="u-font-18 u-p-b-20">{{vuex_user.name||'律总管'}}</view>
				<view class="u-font-14 u-tips-color">{{vuex_user.mobile}}</view>
			</view>
			<view class="u-m-l-10 u-p-10">
				<u-icon name="scan" color="#969799" size="28"></u-icon>
			</view>
			<view class="u-m-l-10 u-p-10">
				<u-icon name="arrow-right" color="#969799" size="28"></u-icon>
			</view>
		</view>
		
		<view class="u-m-t-20">
			<u-cell-group>
				<u-cell-item @click="copy" value="oa.lgy0999.com" :arrow="false" icon="integral" title="电脑端访问"></u-cell-item>
				<u-cell-item @click="goHome" index="../index/index" icon="attach" title="我的案件"></u-cell-item>
				<u-cell-item @click="goHome" index="../calendar/calendar" icon="clock" title="我的待办"></u-cell-item>
			</u-cell-group>
		</view>
		
		<view class="u-m-t-20">
			<u-cell-group>
				<u-cell-item @click="goPage" index="/pages/center/setting/setting" icon="setting" title="账号设置"></u-cell-item>
			</u-cell-group>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				pic:require('@/static/photo.jpeg'),
				show:true
			}
		},
		onShow() {
			this.getUserInfo()
		},
		methods: {
			async getUserInfo(){
				let res = await this.$u.api.common.userInfo()
				if(res){
					this.$u.vuex('vuex_user', {role:res.role,...res.user});
				}
			},
			goPage(url){
				uni.navigateTo({
					url
				})
			},
			goHome(url){
				uni.switchTab({
					url
				})
			},
			copy(){
				uni.setClipboardData({
				      data: 'http://oa.lgy0999.com/',
				      success: function (res) {
				        uni.showToast({
				          title: '已复制网址',
				        });
				      }
				    })
			}
		}
	}
</script>

<style lang="scss">
page{
	background-color: #ededed;
}

.camera{
	width: 54px;
	height: 44px;
	
	&:active{
		background-color: #ededed;
	}
}
.user-box{
	background-color: #fff;
}
</style>
