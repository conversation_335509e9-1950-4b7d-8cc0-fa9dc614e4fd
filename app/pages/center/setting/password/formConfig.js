const formConfig = function(vm) {
	return new Promise(async (resolve, rejict) => {
		let result = [
			{
				title: '修改密码',
				key: 'concernPersonList',
				type: 'Array',
				add: false,
				form: [{
						label: '旧密码',
						key: 'oldPassword',
						name: 'oldPassword',
						value: '',
						type: 'input',
						config: {
							type:'password'
						},
						rules: [{
							required: true,
							message: '请输入旧密码',
							trigger: ['change', 'blur'],
						}]
					},
					{
							label: '新密码',
							key: 'newPassword',
							name: 'newPassword',
							value: '',
							type: 'input',
							config: {
								type:'password'
							},
							rules: [{
								required: true,
								message: '请输入新密码',
								trigger: ['change', 'blur'],
							},{
						min: 6, 
						message: '新密码不能少于6个字符', 
						trigger: 'change'
					}]
						},
				]
			}
		]
		vm.$data.formConfig = result
		resolve(result)
	})
}

export default formConfig
