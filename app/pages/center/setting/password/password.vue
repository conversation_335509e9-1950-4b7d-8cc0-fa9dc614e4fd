<template>
	<view>
		<creatForm ref='creatForm' :formConfig='formConfig' @submit='submit' />
	</view>
</template>

<script>
	import creatForm from '@/components/creatForm.vue'
	import formConfig from './formConfig.js'
	export default {
		components: {
			creatForm
		},
		data() {
			return {
				id:'',
				formConfig: {},
			};
		},
		onLoad(e) {
			formConfig(this)
		},
		methods:{
			async submit(data){
				let res = this.$u.deepClone(data);
				console.log(res)
				let result = await this.$u.api.common.savePwd(res)
				if(result){
					this.$u.toast('提交成功')
					uni.navigateBack()
				}
			}
		}
	}
</script>

<style scoped lang="scss">

</style>
