<template>
	<view class="setting_box">
		<u-cell-group>
				<u-cell-item title="个人信息" @click="goPage('./info/info')"></u-cell-item>
				<u-cell-item title="修改密码" @click="goPage('./password/password')"></u-cell-item>
			</u-cell-group>
			<view class="out">
				<u-button type="primary" @click="goPage('/pages/login/login')">退出账号</u-button>
			</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			};
		},
		methods:{
			async getUserInfo(){
				let res = await this.$u.api.common.userInfo()
				if(res){
					this.$u.vuex('vuex_user', {role:res.role,...res.user});
				}
			},
			async goPage(url){
				if(url == '/pages/login/login'){
					let clientId = plus.push.getClientInfo().clientid
					let res = await this.$u.api.common.logout({clientId})
					this.$u.vuex('vuex_token', '');
					// this.$u.vuex('vuex_dict', {});
					uni.reLaunch({
						url
					})
				}else{
					uni.navigateTo({
						url
					})
				}
				
			}
		},
		onShow() {
			this.getUserInfo()
		},
	}
</script>

<style lang="scss" scoped>
.setting_box{
	height: 100%;
	position: relative;
	.out{
		padding: 30rpx;
		width: 100%;
		position: absolute;
		bottom: 0;
		.u-btn--primary{
			border-radius: 0;
		}
	}
}
</style>
