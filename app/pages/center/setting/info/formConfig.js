const formConfig = function(vm) {
	return new Promise(async (resolve, rejict) => {
		let result = [
			{
				title: '个人信息',
				key: 'concernPersonList',
				type: 'Array',
				add: false,
				form: [{
						label: '姓名',
						key: 'name',
						name: 'name',
						value: '',
						type: 'input',
						config: {},
						rules: [{
							required: true,
							message: '请输入姓名',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '联系方式',
						key: 'mobile',
						name: 'mobile',
						value: '',
						type: 'input',
						config: {
							type:'number'
						},
						rules: [{
							required: true,
							message: '请输入联系方式',
							trigger: ['change', 'blur'],
						}, {
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// this.$u.test.mobile()就是返回true或者false的
								return vm.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change
							trigger: ['change', 'blur'],
						}]
					},
				]
			}
		]
		vm.$data.formConfig = result
		resolve(result)
	})
}

export default formConfig
