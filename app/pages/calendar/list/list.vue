<template>
	<view>
		<block v-for="(item,key) in list">
			<u-card :key='key' :title="item.name" :sub-title="item.remindDate" >
				<view class="" slot="body" style="line-height: 50rpx;">
					<view>案件阶段：{{item.stage.name}}</view>
					<view>关联案件：{{item.lawCase.name}}</view>
					<view v-if="item.content">工作详情：{{item.content}}</view>
				</view>
			</u-card>
			
		</block>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				daty:'',
				list:[]
			};
		},
		onLoad(e) {
			this.day = e.day
		},
		onShow() {
			this.dayList()
		},
		methods:{
			async dayList() {
				let res = await this.$u.api.calendar.dayList(this.day)
				if(res){
					this.list = res.data
				}
			}
		}
	}
</script>

<style lang="scss">

</style>
