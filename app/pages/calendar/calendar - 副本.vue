<template>
    <view class="page-wrap">
        <scroll-list ref="list" :option="option" @load="load" @refresh="refresh">
            <view class="list-item" v-for="(item, index) in list" :key="index" @click="handleTest">
                <view class="avatar">{{ index + 1 }}</view>
                <view class="info">
                    <view class="info-item"></view>
                    <view class="info-item"></view>
                </view>
            </view>
        </scroll-list>
    </view>
</template>

<script>
export default {
    data() {
        return {
			list:[],
            option: {
                size: 20,
                auto: true
            },
        };
    },
    onLoad() {
    },
    onHide() {},
    methods: {
        // 加载数据
        load(paging) {
            setTimeout(() => {
                let list = [];
                for (var i = 0; i < paging.size; i++) {
                    list.push(i);
                }
                this.list = [...this.list, ...list];
                // 加载成功  参数对象{list: 当前列表,total: 数据总长度(后端查询的total)}
                this.$refs.list.loadSuccess({ list: this.list, total: 50 });
                // 加载失败
                // this.$refs.list.loadFail()
            }, this.$u.random(100, 1000));
        },
        // 刷新刷剧
        refresh(paging) {
            setTimeout(() => {
                let list = [];
                for (var i = 0; i < paging.size; i++) {
                    list.push(i);
                }
                this.list = list;
                // 刷新成功  参数对象{list: 当前列表,total: 数据总长度(后端查询的total)}
                this.$refs.list.refreshSuccess({ list: this.list, total: 50 });
                // 刷新失败
                // this.$refs.list.refreshFail()
            }, this.$u.random(100, 1000));
        }
    }
};
</script>

<style>

</style>
