<template>
	<view class="content" style="background-color: #FFFFFF;" >
		<zwyCalendar ref='date' @changeMonth='getData' type="sign" :checkDate="true" bgweek="#006afe" bgday="#006afe" @change="signDate" />
		<!-- <u-empty text="暂无数据" mode="list"></u-empty> -->
	</view>
</template>
<script>
	import zwyCalendar from '@/components/zwy-calendar/zwy-calendar.vue'
	export default{
		components: {zwyCalendar},
		data(){
			return{
				month:this.$u.timeFormat(new Date(), 'yyyy-mm'),
			}
		},
		created() {
			// this.getData()
		},
		onPullDownRefresh(){
			this.getData(this.month)
		},
		
		methods:{
			async getData(month){
				this.month = month
				let res = await this.$u.api.calendar.monthList(month)
				if(res){
					let arr = Object.values(res.data).flat()
					// this.$u.timeFormat(new Date(), 'dd')
					setTimeout(()=>{
						this.$refs.date.dayArr.map(item=>{
							item['list'] = []
						})
						this.$refs.date.dayArr.map(item=>{
							arr.forEach(i=>{
								// let start = this.$u.timeFormat(i.startDate, 'yyyy-mm-dd')
								// let end = this.$u.timeFormat(i.endDate, 'yyyy-mm-dd')
								let remindDate = this.$u.timeFormat(i.remindDate, 'yyyy-mm-dd')
								if(item.date==remindDate){
									item['list'].push(i)
								}
							})
						})
						uni.stopPullDownRefresh()
					},0)
				}
			},
			async signDate(event) {
				if(event){
					let res = await this.$u.api.calendar.dayList(event)
					if(res){
						if(res.data.length>0){
							uni.navigateTo({
								url:`./list/list?day=${event}`
							})
						}
					}
				}
			}
		}
	}
	
</script>