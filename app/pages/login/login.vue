<!-- 蓝色简洁登录页面 -->
<template>
	<view class="t-login">
		<!-- 页面装饰图片 -->
		<image class="img-a" src="@/static/2.png"></image>
		<image class="img-b" src="@/static/3.png"></image>
		<!-- 标题 -->
		<view class="t-b">{{ title }}</view>
		<form class="cl">
			<view class="t-a">
				<image src="@/static/zh.png"></image>
				<input name="userName" placeholder="请输入账号" maxlength="11" v-model="userName" />
			</view>
			<view class="t-a">
				<image src="@/static/yz.png"></image>
				<input type="password" name="password" maxlength="16" placeholder="请输入密码" v-model="password" />
			</view>
			<button @tap="login()">登 录</button>
		</form>
	</view>
</template>
<script>
export default {
	/**
	 * 2020年12月1日   李新雷编写（练习）  适用所有app登录
	 * vue版本简洁又美观的登录页面（个人感觉插件市场的登录都不太好看，哈哈 O(∩_∩)O）
	 * 该模板只是登录模板：验证、倒计时等都已经写好，
	 * 如果需要注册（注册可以设计在登录按钮下方），
	 * 直接复制该页面稍微改动即可
	 */
	data() {
		return {
			title: '欢迎回来！', //填写logo或者app名称，也可以用：欢迎回来，看您需求
			second: 60, //默认60秒
			showText: true, //判断短信是否发送
			userName: '', //手机号码
			password: '' //验证码
		};
	},
	onLoad() {
		// manager
		// this.getUserInfo()
	},
	onShow() {
		if(this.vuex_token){
			uni.switchTab({
				url:'../index/index'
			})
		}
	},
	methods: {
		//当前登录按钮操作
		async login() {
			var that = this;
			if (!that.userName) {
				uni.showToast({ title: '请输入账号', icon: 'none' });
				return;
			}
			if (!that.password) {
				uni.showToast({ title: '请输入密码', icon: 'none' });
				return;
			}
			// #ifdef APP-PLUS
			let clientId = plus.push.getClientInfo().clientid
			// #endif
			let res = await this.$u.api.common.login({
			  	userName:this.userName,
			  	password:this.password,
				// #ifdef APP-PLUS
			  	clientId,
				// #endif
			  })
			if(res){
				console.log(res)
				this.$u.vuex('vuex_token', res.token);
				console.log(this.vuex_token)
				this.getUserInfo()
			}
			//....此处省略，这里需要调用后台验证一下验证码是否正确，根据您的需求来
		},
		async getUserInfo(){
			let res = await this.$u.api.common.userInfo()
			if(res){
				this.$u.vuex('vuex_user', {role:res.role,...res.user});
				this.getDictMap()
				// uni.switchTab({
				// 	url:'/pages/index/index'
				// })
				// uni.showToast({ title: '登录成功！', icon: 'none' });
			}
		},
		async getDictMap(){
			let res = await this.$u.api.common.getDictMap()
			if(res){
				this.$u.vuex('vuex_dict', res.dictList);
				uni.switchTab({
					url:'/pages/index/index'
				})
				uni.showToast({ title: '登录成功！', icon: 'none' });
			}
		}
	}
};
</script>
<style scoped>
.img-a {
	position: absolute;
	width: 100%;
	top: -230rpx;
	right: -100rpx;
}
.img-b {
	position: absolute;
	width: 50%;
	bottom: -200rpx;
	left: -50rpx;
}
.t-login {
	background-color: #FFFFFF;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	color: #000;
	height: 100%;
	position: relative;
	overflow: hidden;
}

.t-login button {
	font-size: 28rpx;
	background: #5677fc;
	color: #fff;
	height: 90rpx;
	line-height: 90rpx;
	border-radius: 50rpx;
	box-shadow: 0 5px 7px 0 rgba(86, 119, 252, 0.2);
}

.t-login input {
	padding: 0 20rpx 0 120rpx;
	height: 90rpx;
	line-height: 90rpx;
	margin-bottom: 50rpx;
	background: #f8f7fc;
	border: 1px solid #e9e9e9;
	font-size: 28rpx;
	border-radius: 50rpx;
}

.t-login .t-a {
	position: relative;
}

.t-login .t-a image {
	width: 40rpx;
	height: 40rpx;
	position: absolute;
	left: 40rpx;
	top: 28rpx;
	border-right: 2rpx solid #dedede;
	padding-right: 20rpx;
}

.t-login .t-b {
	width: 600rpx;
	text-align: left;
	font-size: 46rpx;
	color: #000;
	padding: 0 0 120rpx 0;
	font-weight: bold;
}

.t-login .t-c {
	position: absolute;
	right: 22rpx;
	top: 22rpx;
	background: #5677fc;
	color: #fff;
	font-size: 24rpx;
	border-radius: 50rpx;
	height: 50rpx;
	line-height: 50rpx;
	padding: 0 25rpx;
}

.t-login .t-d {
	text-align: center;
	color: #999;
	margin: 80rpx 0;
}

.t-login .t-e {
	text-align: center;
	width: 250rpx;
	margin: 80rpx auto 0;
}

.t-login .t-g {
	float: left;
	width: 50%;
}

.t-login .t-e image {
	width: 50rpx;
	height: 50rpx;
}

.t-login .t-f {
	text-align: center;
	margin: 200rpx 0 0 0;
	color: #666;
}

.t-login .t-f text {
	margin-left: 20rpx;
	color: #aaaaaa;
	font-size: 27rpx;
}

.t-login .uni-input-placeholder {
	color: #000;
}

.cl {
	zoom: 1;
	width: 600rpx;
	padding-bottom: 100rpx;
}

.cl:after {
	clear: both;
	display: block;
	visibility: hidden;
	height: 0;
	content: '\20';
}
</style>
