<template>
	<view>
		<creatForm ref='creatForm' :formConfig='formConfig' @confirm='confirm' @submit='submit'>
			<u-cell-item title="" @click='show=true' hover-class='none'>
				<u-form-item label="跟进人">
					<view class="u-line-1 u-text-left">
						<block v-for="(item,key) in list" :key='key'>
							{{item.checked?`${item.name},`:''}}
						</block>
					</view>
				</u-form-item>
			</u-cell-item>
		</creatForm>
		<u-popup v-model="show" mode='right'>
			<view style="width:300rpx;">
				<view class="u-padding-15 u-font-30 u-text-center u-border-bottom" style="font-weight: bold;">选择跟进人</view>
				<u-checkbox-group wrap  >
					<view class="u-padding-15 u-border-bottom" v-for="(item, index) in list" :key="index">
						<u-checkbox style="width: 100%;" shape='circle' v-model="item.checked"  :name="item.name">
							<view style="width: 100vw;">{{item.name}}</view>
							</u-checkbox>
					</view>
				</u-checkbox-group>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import creatForm from '@/components/creatForm.vue'
	import formConfig from './formConfig.js'
	export default {
		components: {
			creatForm
		},
		data() {
			return {
				show: false,
				id: '',
				gid: '',
				formConfig: {},
				formObj: {},
				info: {},
				customerFollowUpList: [],
				list: []
			};
		},
		onLoad(e) {
			this.id = e.id
			formConfig(this)
		},
		onShow() {
			this.customerInfo()
			this.industryData()
			this.allBriefList()
		},
		methods: {
			async customerInfo() {
				let res = await this.$u.api.lawCase.customerInfo(this.id)
				if (res) {
					this.customerFollowUpList = res.data.customerFollowUpList
					console.log(res, '132323')
					Object.assign(this.info, {
						...res.data.customer,
						type: this.vuex_dict.customer_type.find(dict => dict.value == res.data.customer.type)[
							'label'],
						status: this.vuex_dict.cooperate_status.find(dict => dict.value == res.data.customer
							.status)['label'],
						source: this.vuex_dict.customer_source.find(dict => dict.value == res.data.customer
							.source)['label'],
					})
					this.confirm({
						name: 'status',
						label: this.vuex_dict.cooperate_status.find(dict => dict.value == res.data.customer
							.status)['label'],
						value: this.vuex_dict.cooperate_status.find(dict => dict.value == res.data.customer
							.status)['value'],
					})
					this.confirm({
						name: 'type',
						label: this.vuex_dict.customer_type.find(dict => dict.value == res.data.customer.type)[
							'label'],
						value: this.vuex_dict.customer_type.find(dict => dict.value == res.data.customer.type)[
							'value'],
					})
					this.confirm({
						name: 'source',
						label: this.vuex_dict.customer_source.find(dict => dict.value == res.data.customer
							.source)['label'],
						value: this.vuex_dict.customer_source.find(dict => dict.value == res.data.customer
							.source)['value'],
					})
					this.confirm({
						name: 'industry',
						label: res.data.customer.industry.name,
						value: res.data.customer.industry.id,
					})
					this.formConfig[0].form.map(item => {
						if (item.key == 'industry') {
							item['value'] = this.info[item.key].name
							this.$refs['creatForm'].form[item.key] = this.info[item.key].name
						} else {
							item['value'] = this.info[item.key]
							this.$refs['creatForm'].form[item.key] = this.info[item.key]
						}
						return item
					})
				}
			},
			// 所属行业
			async industryData() {
				let res = await this.$u.api.lawCase.industryData()
				if (res) {
					this.formConfig[0].form.map(item => {
						if (item.key == 'industry') {
							item.list = res.treeData
						}
					})
				}
			},
			// 跟进人列表
			async allBriefList() {
				let res = await this.$u.api.lawCase.allBriefList()
				if (res) {
					let defauleValue = this.customerFollowUpList.map(item => item.user.id);
					console.log(defauleValue)
					this.list = res.data.map(item => {
						if (defauleValue.includes(item.id)) {
							item['checked'] = true
						} else {
							item['checked'] = false
						}
						return item
					})
				}
			},
			confirm(res) {
				this.formObj = {
					...this.formObj,
					[res['name']]: {
						id: res.value,
						name: res.label,
					}
				}
			},
			async submit(data) {
				let res = this.$u.deepClone(data);
				let o = [
					'type',
					'status',
					'source',
				]
				Object.keys(this.formObj).forEach((key) => {
					if (o.includes(key)) {
						res[key] = this.formObj[key]['id']
					}
				})
				let industry = this.formConfig[0].form.find(item => item.key == 'industry')
				
				res['industry'] = this.formObj.industry
				res['id'] = this.id
				let customerFollowUpList = this.list.filter(item=>item.checked==true)
				let sub = {
					customer:{...res},
					customerFollowUpList:customerFollowUpList.map(item=>{
						return {
							id:'',
							user:{
								id:item.id,
								name:item.name,
							}
						}
					})
				}
				console.log(sub)
				let result = await this.$u.api.lawCase.editCustomer(sub)
				if(result){
					this.$u.toast('提交成功')
					uni.navigateBack()
				}
			}
		}
	}
</script>

<style scoped lang="scss">

</style>
