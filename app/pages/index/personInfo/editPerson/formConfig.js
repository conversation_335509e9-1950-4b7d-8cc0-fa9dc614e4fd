const formConfig = function(vm) {
	return new Promise(async (resolve, rejict) => {
		let result = [
			{
				title: '',
				key: 'concernPersonList',
				type: 'Array',
				add: false,
				form: [{
						label: '客户名称',
						key: 'name',
						name: 'name',
						value: '',
						type: 'input',
						config: {},
						rules: [{
							required: true,
							message: '请输入客户名称',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '联系方式',
						key: 'phone',
						name: 'phone',
						value: '',
						type: 'input',
						config: {
							type:'number'
						},
						rules: [{
							required: true,
							message: '请输入联系方式',
							trigger: ['change', 'blur'],
						}, {
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// this.$u.test.mobile()就是返回true或者false的
								return vm.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '客户标识',
						key: 'type',
						name: 'type',
						value: '',
						type: 'select',
						config: {
							mode: 'single-column',
							label: 'label',
							value: 'value'
						},
						list: vm.vuex_dict.customer_type,
						rules: [{
							required: true,
							message: '请选择类型',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '合作状态',
						key: 'status',
						name: 'status',
						value: '',
						type: 'select',
						config: {
							mode: 'single-column',
							label: 'label',
							value: 'value'
						},
						list: vm.vuex_dict.cooperate_status,
						rules: [{
							required: true,
							message: '合作状态',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '客户来源',
						key: 'source',
						name: 'source',
						value: '',
						type: 'select',
						config: {
							mode: 'single-column',
							label: 'label',
							value: 'value'
						},
						list: vm.vuex_dict.customer_source,
						rules: [{
							required: true,
							message: '客户来源',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '所属行业',
						key: 'industry',
						name: 'industry',
						value: '',
						type: 'select',
						config: {
							mode: 'mutil-column-auto',
							label: 'name',
							value: 'id'
						},
						list: [],
						rules: [{
							required: true,
							message: '所属行业',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '合同开始日期',
						key: 'contractStartDate',
						name: 'contractStartDate',
						value: '',
						type: 'picker',
						config: {
							mode: 'time',
							params: {
								year: true,
								month: true,
								day: true,
							},
						},
						list: [],
						rules: [{
							required: true,
							message: '请选择合同开始日期',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '合同结束日期',
						key: 'contractEndDate',
						name: 'contractEndDate',
						value: '',
						type: 'picker',
						config: {
							mode: 'time',
							params: {
								year: true,
								month: true,
								day: true,
							},
						},
						list: [],
						rules: [{
							required: true,
							message: '请选择合同结束日期',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '备注',
						key: 'remarks',
						name: 'remarks',
						value: '',
						type: 'input',
						config: {},
						rules: [{
							required: true,
							message: '请输入备注',
							trigger: ['change', 'blur'],
						}]
					},
				]
			}
		]
		vm.$data.formConfig = result
		resolve(result)
	})
}

export default formConfig
