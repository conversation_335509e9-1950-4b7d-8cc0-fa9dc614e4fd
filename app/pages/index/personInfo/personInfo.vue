<template>
	<view class="content">
		<view class="u-flex-1">
			<u-modal @confirm='confirm' v-model="show" content="是否删除客户？" show-cancel-button></u-modal>
			<u-cell-group v-if="info.customer">
				<u-cell-item title="客户名称" :arrow="false" :value="info.customer.name"></u-cell-item>
				<u-cell-item title="联系方式" :arrow="false" :value="info.customer.phone"></u-cell-item>
				<u-cell-item title="客户标识" :arrow="false" :value="vuex_dict.customer_type.find(dict=>dict.value==info.customer.type)['label']"></u-cell-item>
				<u-cell-item title="合作状态" :arrow="false" :value="vuex_dict.cooperate_status.find(dict=>dict.value==info.customer.status)['label']"></u-cell-item>
				<u-cell-item title="客户来源" :arrow="false" :value="vuex_dict.customer_source.find(dict=>dict.value==info.customer.source)['label']"></u-cell-item>
				<u-cell-item title="所属行业" :arrow="false" :value="info.customer.industry.name"></u-cell-item>
				<u-cell-item title="合同开始日期" :arrow="false" :value="info.customer.contractStartDate"></u-cell-item>
				<u-cell-item title="合同结束日期" :arrow="false" :value="info.customer.contractEndDate"></u-cell-item>
				<u-cell-item title="备注" :arrow="false" :value="info.customer.remarks"></u-cell-item>
			</u-cell-group>
		</view>
		<view class="u-padding-30">
			<u-button type="primary" @click="goPage">编辑</u-button>
			<u-button class="u-margin-top-30" type="error" @click="show=true">删除</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id:'',
				gid:'',
				info:'',
				show:false
			};
		},
		onLoad(e) {
			this.id = e.id
		},
		onShow() {
			this.customerInfo()
		},
		methods:{
			goPage(){
				uni.navigateTo({
					url:`./editPerson/editPerson?id=${this.id}`
				})
			},
			async customerInfo(){
				let res = await this.$u.api.lawCase.customerInfo(this.id)
				if(res){
					this.info = res.data
				}
			},
			async confirm(){
				let res = await this.$u.api.lawCase.deleteCustomer(this.id)
				if(res){
					uni.navigateBack()
					this.$u.toast('删除成功')
				}
			}
		}
	}
</script>

<style lang="scss">

</style>
