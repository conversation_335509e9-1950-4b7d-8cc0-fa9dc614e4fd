const formConfig = function(vm) {
	return new Promise(async (resolve, rejict) => {
		let result = [{
				title: '基础信息',
				key: 'lawCase',
				type: 'Object',
				form: [{
						label: '案件名称',
						key: 'name',
						name: 'name',
						value: '',
						type: 'input',
						config: {},
						rules: [{
							required: true,
							message: '请输入案件名称',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '案件类型',
						key: 'type',
						name: 'type',
						value: '',
						type: 'select',
						config: {
							mode: 'single-column',
							label: 'label',
							value: 'value'
						},
						list: vm.vuex_dict.case_type,
						rules: [{
							required: true,
							message: '请选择案件类型',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '案件程序',
						key: 'caseProgram',
						name: 'caseProgram',
						value: '',
						type: 'select',
						config: {
							mode: 'single-column',
							label: 'label',
							value: 'value'
						},
						list: [],
						rules: [{
							required: true,
							message: '请选择案件程序',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '案由(临时选用)',
						key: 'caseCause',
						name: 'caseCause',
						value: '',
						type: 'select',
						config: {
							mode: 'mutil-column-auto',
							label: 'name',
							value: 'id'
						},
						list: [],
						rules: [{
							required: true,
							message: '请选择案由',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '主办人员',
						key: 'hostUser',
						name: 'hostUser',
						value: '',
						type: 'select',
						config: {
							mode: 'single-column',
							label: 'name',
							value: 'id'
						},
						list: [],
						rules: [{
							required: true,
							message: '请选择主办人员',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '委托时间',
						key: 'entrustDate',
						name: 'entrustDate',
						value: '',
						type: 'picker',
						config: {
							mode: 'time',
							params: {
								year: true,
								month: true,
								day: true,
							},
						},
						list: [],
						rules: [{
							required: true,
							message: '请选择委托时间',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '案件标的',
						key: 'subjectMatter',
						name: 'subjectMatter',
						value: '',
						type: 'input',
						config: {
							rightText: '元',
							type: 'digit'
						},
						rules: [{
							required: true,
							message: '请输入案件标的',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '合同金额',
						key: 'contractMoney',
						name: 'contractMoney',
						value: '',
						type: 'input',
						config: {
							rightText: '元',
							type: 'digit'
						},
						rules: [{
							required: true,
							message: '请输入合同金额',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '受理单位',
						key: 'acceptUnitName',
						name: 'acceptUnitName',
						value: '',
						type: 'input',
						config: {},
						rules: [{
							required: true,
							message: '请输入受理单位',
							trigger: ['change', 'blur'],
						}]
					},
				]
			},
			{
				title: '案件当事人',
				key: 'concernPersonList',
				type: 'Array',
				add: true,
				form: [{
						label: '当事人',
						key: 'name',
						name: 'name',
						value: '',
						type: 'input',
						config: {},
						rules: [{
							required: true,
							message: '请输入当事人姓名',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '联系方式',
						key: 'phone',
						name: 'phone',
						value: '',
						type: 'input',
						config: {
							type:'number'
						},
						rules: [{
							required: true,
							message: '请输入联系方式',
							trigger: ['change', 'blur'],
						}, {
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// this.$u.test.mobile()就是返回true或者false的
								return vm.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '当事人类型',
						key: 'type',
						name: 'type',
						value: '',
						type: 'select',
						config: {
							mode: 'single-column',
							label: 'label',
							value: 'value'
						},
						list: vm.vuex_dict.customer_type,
						rules: [{
							required: true,
							message: '请选择类型',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '委托方',
						key: 'isEntrust',
						name: 'isEntrust',
						value: '',
						type: 'select',
						config: {
							mode: 'single-column',
							label: 'label',
							value: 'value'
						},
						list: vm.vuex_dict.yes_no,
						rules: [{
							required: true,
							message: '是否委托方',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '备注',
						key: 'attribute',
						name: 'attribute',
						value: '',
						type: 'input',
						config: {},
						rules: [{
							required: true,
							message: '请输入备注',
							trigger: ['change', 'blur'],
						}]
					},
				]
			}
		]
		vm.$data.formConfig = result
		resolve(result)
	})
}

export default formConfig
