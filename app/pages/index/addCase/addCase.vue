<template>
	<view class="box">
		<creatForm ref='creatForm' :formConfig='formConfig' @add='add' @confirm='confirm' @submit='submit' />
	</view>
</template>

<script>
	import creatForm from '@/components/creatForm.vue'
	import formConfig from './formConfig.js'
	export default {
		components: {
			creatForm
		},
		data() {
			return {
				formConfig: {},
				caseProgram: [],
				formObj: {}
			};
		},
		created() {
			formConfig(this)
			this.init()
		},
		methods: {
			init() {
				this.treeData()
				this.allBriefList()
				let len = this.formConfig.length
				this.formConfig.map((item, key) => {
					item.form.map(i => {
						if (item.type == 'Array') {
							i.name = `${item.key}-${i.key}-${len-2} `
						} else {
							i.name = `${item.key}-${i.key}`
						}
					})
				})
			},
			add() {
				let len = this.formConfig.length
				let arr = this.$u.deepClone(this.formConfig[1]);
				arr.form.map(i => {
					i.name = `${arr.key}-${i.key}-${len-1} `
				})
				this.formConfig.push(arr)
				console.log(arr, '添加')
			},
			confirm(res) {
				this.formObj = {
					...this.formObj,
					[res['name']]: {
						id: res.value,
						name: res.label,
					}
				}
				if (res.name == 'lawCase-type') {
					this.$refs['creatForm'].form['lawCase-caseProgram'] = ''
					this.formConfig[0].form[2].list = []
					this.typeTreeData(res.value)
				}
			},
			getDictList(row) {
				// debugger
				row.map(item => {
					this.formConfig[0].form[2].list = [...this.formConfig[0].form[2].list, {
						label: item.name,
						value: item.id,
					}]
					if (item.children && item.children.length > 0) {
						this.getDictList(item.children)
					}
				})
			},
			// 案件程序列表
			async typeTreeData(type) {
				let res = await this.$u.api.lawCase.typeTreeData(type)
				if (res) {
					this.getDictList(res.treeData)
				}
			},
			// 案由
			async treeData() {
				let res = await this.$u.api.lawCase.treeData()
				if (res) {
					this.formConfig[0].form[3].list = res.treeData
				}
			},
			// 主办人员
			async allBriefList() {
				let res = await this.$u.api.lawCase.allBriefList()
				if (res) {
					this.formConfig[0].form[4].list = res.data
					// this.formConfig[0].form[3].list = res.treeData
				}
			},
			async submit(data) {
				console.log('32323')
				let res = this.$u.deepClone(data);
				let form = {
					lawCase: {},
					concernPersonList: [],
				}
				let o = [
					'lawCase-type',
					'concernPersonList-type',
					'concernPersonList-isEntrust',
				]
				Object.keys(this.formObj).forEach((key)=>{
					let result = key.split('-')
					var str = `${result[0]}-${result[1]}`
					res[key] = this.formObj[key]
					if(o.includes(str)){
						res[key] = this.formObj[key]['id']
					}
				})
				Object.keys(res).forEach((key) => {
					let result = key.split('-')
					if (result[0] == 'concernPersonList') {
						form['concernPersonList'][result[2] - 0] = {
							...form['concernPersonList'][result[2] - 0],
							[result[1]]: res[key]
						}
					} else {
						if(result[1] == 'entrustDate'){
							form[result[0]][result[1]] = res[key] + ' 00:00:00'
						}else{
							form[result[0]][result[1]] = res[key]
						}
					}
					console.log(key.split('-')) // foo
				})
				console.log(form)
				let res1 = await this.$u.api.lawCase.saveInfo(form)
				if(res1){
					uni.navigateBack({})
					this.$u.toast('创建成功')
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.box {
		height: 100%;
	}
</style>
