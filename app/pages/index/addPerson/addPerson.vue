<template>
	<view>
		<creatForm ref='creatForm' :formConfig='formConfig' @confirm='confirm' @submit='submit' />
	</view>
</template>

<script>
	import creatForm from '@/components/creatForm.vue'
	import formConfig from './formConfig.js'
	export default {
		components: {
			creatForm
		},
		data() {
			return {
				id:'',
				formConfig: {},
				formObj: {}
			};
		},
		onLoad(e) {
			this.id = e.id
			formConfig(this)
			this.industryData()
		},
		methods:{
			confirm(res) {
				this.formObj = {
					...this.formObj,
					[res['name']]: {
						id: res.value,
						name: res.label,
					}
				}
			},
			// 所属行业
			async industryData() {
				let res = await this.$u.api.lawCase.industryData()
				if (res) {
					this.formConfig[0].form.map(item=>{
						if(item.key == 'industry'){
							item.list = res.treeData
						}
					})
				}
			},
			async submit(data){
				let res = this.$u.deepClone(data);
				let o = [
					'type',
					'status',
					'source',
				]
				Object.keys(this.formObj).forEach((key)=>{
					res[key] = this.formObj[key]
					if(o.includes(key)){
						res[key] = this.formObj[key]['id']
					}
				})
				console.log(res)
				let result = await this.$u.api.lawCase.addCustomer(res)
				if(result){
					this.$u.toast('提交成功')
					uni.navigateBack()
				}
			}
		}
	}
</script>

<style scoped lang="scss">

</style>
