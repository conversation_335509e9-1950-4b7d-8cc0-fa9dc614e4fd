<template>
	<view class="list">
		<u-select v-model="personStatus" @confirm="confirm" z-index='99999' value-name='id' label-name='name' :list="personList"></u-select>
		<u-popup v-model="show" mode="right">
			<view class="pop_box">
				<view class="u-padding-top-20 u-padding-bottom-20 u-font-30" style="color:#8a8a8a ;">当事人</view>
				<u-input v-model="queryConcernPersonName" :border="true" />
				<view class="u-padding-top-20 u-padding-bottom-20 u-font-30" style="color:#8a8a8a ;">案件名称</view>
				<u-input v-model="name" :border="true" />
				<view class="u-padding-top-20 u-padding-bottom-20 u-font-30" style="color:#8a8a8a ;">案号</view>
				<u-input v-model="number" :border="true" />
				<view class="u-padding-top-20 u-padding-bottom-20 u-font-30" style="color:#8a8a8a ;">承办律师</view>
				<u-input v-model="person" disabled @click="personStatus=true" :border="true" placeholder="请选择承办律师" />
				<view class="u-padding-top-40"></view>
				<u-row gutter="1">
					<u-col span="6">
						<u-button size="medium" plain @click="initSearch">重置</u-button>
					</u-col>
					<u-col span="6">
						<u-button size="medium" type="primary" @click="refresh">确定</u-button>
					</u-col>
				</u-row>
			</view>
		</u-popup>
		<view class="u-flex" style="background-color: #FFFFFF;">
			<u-dropdown>
				<u-dropdown-item v-model="value1" title="共办" :options="options1">
				</u-dropdown-item>
				<u-dropdown-item v-model="value4" title="类型" :options="options4">
				</u-dropdown-item>
				<u-dropdown-item v-model="value3" title="年度" :options="options3">
				</u-dropdown-item>
				<u-dropdown-item v-model="value2" title="状态" :options="options2">
				</u-dropdown-item>
			</u-dropdown>
			<view class="u-flex u-padding-right-30 u-font-26" @click="show = true">
				<text style="color: #606266;">筛选</text>
				<image style="width: 40rpx;height: 40rpx;" src="../../../static/img/sx.png"></image>
			</view>
		</view>
		<MyList ref="list" :option="option" @load="load" @refresh="refresh">
			<u-card @click="goPage(item.lawCase.id,item.lawCase.name)" :title="item.typename"
				:sub-title="item.lawCase.createDate" v-for="(item,i) in list" :key='i'>
				<view class="" slot="body">
					<view class="u-m-t-20">案件名称：{{item.lawCase.name}}</view>
					<view class="u-m-t-20">受理单位：{{item.lawCase.acceptUnitName}}</view>
				</view>
				<view class="u-flex u-row-between" slot="foot">
					<view class="u-line-1" style="width: 380rpx;">委托方：{{item.lawCase.entrustPersonNames}}</view>
					<view>代理律师：{{item.lawCase.hostUser.name}}</view>
				</view>
			</u-card>
		</MyList>
		<block v-if="list.length<1">
			<u-empty text="暂无数据" mode="list"></u-empty>
		</block>

	</view>
</template>

<script>
	import MyList from '@/components/MyList.vue'
	export default {
		name: 'list1',
		components: {
			MyList
		},
		data() {
			return {
				personStatus:false,
				show: false,
				queryConcernPersonName: '',
				name: '',
				number: '',
				person:'',
				personId:'',
				personList:[],
				list: [],
				option: {
					pageNo: 1,
					pageSize: 15,
				},
				value1: "",
				value2: '',
				value3: '',
				value4: '',
				options1: [{
						label: '全部',
						value: '',
					}, {
						label: '分给他人',
						value: 1,
					},
					{
						label: '他人分给',
						value: 2,
					},
				],
				options2: [],
				options3: [],
				options4: [{
						label: '全部',
						value: '',
					}, {
						label: '民事案件',
						value: 1,
					},
					{
						label: '刑事案件',
						value: 3,
					},
					{
						label: '行政案件',
						value: 4,
					},
					{
						label: '其他案件',
						value: 99,
					}
				],
			}
		},
		computed: {
			title1() {
				return this.options1.find(item => item.value == this.value1).label
			},
			title2() {
				if (this.options2.length < 1) {
					return ''
				} else {
					return this.options2.find(item => item.value == this.value2).label
				}
			}
		},
		watch: {
			value1() {
				this.refresh()
			},
			value2() {
				this.refresh()
			},
			value3() {
				this.refresh()
			},
			value4() {
				this.refresh()
			},
		},
		created() {
			let arr = [{
				label: '全部',
				value: '',
			}, ]
			this.options2 = [...arr, ...this.vuex_dict.case_audit_status]
			let ar3 = [{
				label: '全部',
				value: '',
			}, ]
			for (let i = 1; i < 11; i++) {
				ar3.push({
					label: 2020 + i,
					value: 2020 + i,
				})
			}
			this.options3 = ar3
			this.allBriefList()
			console.log(this.vuex_dict.case_status)
		},
		methods: {
			confirm(e) {
				this.personId = e[0].value
				this.person =  e[0].label
				console.log(e);
			},
			async allBriefList(){
				let res = await this.$u.api.lawCase.allBriefList()
				if(res){
					this.personList = res.data
					console.log(res.data,'***********')
				}
			},
			goPage(id, name) {
				uni.navigateTo({
					url: `/pages/index/caseInfo/caseInfo?id=${id}&name=${name}`
				})
			},
			async load(paging) {
				let res = await this.$u.api.lawCase.list({
					...paging,
					isMobile: 1,
					checkWord: this.value1,
					auditStatus: this.value2,
					queryYear: this.value3,
					type: this.value4,
					queryConcernPersonName:this.queryConcernPersonName,
					name:this.name,
					number:this.number,
					'hostUser.id':this.personId
				})
				if (res) {
					res.page.list.map(item => {
						item['typename'] = (this.vuex_dict.case_type.find(dict => dict.value == item.lawCase
							.type))['label']
					})
					console.log(res, '+++++++++')
					this.list = [...this.list, ...res.page.list]
					this.$refs.list.loadSuccess({
						list: this.list,
						total: res.page.count
					});

				}
			},
			initSearch(){
				this.queryConcernPersonName = ''
				this.name = ''
				this.number = ''
				this.person = ''
				this.personId = ''
				this.list = []
				this.value1 = ''
				this.value2 = ''
				this.value3 = ''
				this.value4 = ''
				this.refresh()
			},
			async refresh(paging = this.option) {
				this.list = []
				this.load(paging)
				this.show = false
			}
		}
	}
</script>

<style lang="scss" scoped>
	/deep/ .u-dropdown__content {
		width: 100vw;
	}

	.list {
		display: flex;
		flex-direction: column;
		height: 100%;
		box-sizing: border-box;

		.pop_box {
			padding: 0 30rpx;
			width: 500rpx;
		}
	}
</style>
