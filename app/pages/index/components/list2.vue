<template>
	<view class="list">
		<view style="background-color: #FFFFFF;">
			<u-dropdown>
				<u-dropdown-item v-model="value1" :title="title1" :options="options1">
				</u-dropdown-item>
				<u-dropdown-item v-model="value2" :title="title2" :options="options2">
				</u-dropdown-item>
			</u-dropdown>
		</view>
		<MyList  ref="list" :option="option" @load="load" @refresh="refresh">
			<u-card @click="goPage(item.customer.id)" :title="item.customer.name" :sub-title="item.customer.phone" v-for="(item,i) in list" :key='i'>
				<view class="" slot="body">
					<view class="u-flex u-row-between">
						<text>客户标识：</text>
						<text>{{item.customer.type}}</text>
					</view>
					<view class="u-flex u-row-between u-margin-top-20">
						<text>合作状态：</text>
						<text>{{item.customer.status}}</text>
					</view>
					<view class="u-flex u-row-between u-margin-top-20">
						<text>客户来源：</text>
						<text>{{item.customer.source}}</text>
					</view>
				</view>

			</u-card>
		</MyList>
		<block v-if="list.length<1">
			<u-empty text="暂无数据" mode="list"></u-empty>
		</block>
	</view>
</template>

<script>
	import MyList from '@/components/MyList.vue'
	export default {
		name: 'list2',
		components: {
			MyList
		},
		data() {
			return {
				list: [],
				option: {
					pageNo: 1,
					pageSize: 15
				},
				value1: '',
				value2: '',
				options1: [
					{
							label: '全部',
							value: '',
						},
					{
						label: '我的',
						value: 1,
					},
					{
						label: '共享',
						value: 2,
					},
				],
				options2: [{
						label: '全部',
						value: 1,
					},
					{
						label: '单位',
						value: 2,
					},
					{
						label: '个人',
						value: 3,
					},
				],
			}
		},
		created() {
			let arr = [{
						label: '全部',
						value: '',
					},]
			this.options1 = [...arr,...this.vuex_dict.customer_type]
			this.options2 = [...arr,...this.vuex_dict.cooperate_status]
			console.log(this.vuex_dict.customer_type)
		},
		computed:{
			title1(){
				return this.options1.find(item=>item.value==this.value1).label
			},
			title2(){
				return this.options2.find(item=>item.value==this.value2).label
			}
		},
		watch:{
			value1(){
				this.refresh()
			},
			value2(){
				this.refresh()
			},
		},
		methods: {
			goPage(id){
				uni.navigateTo({
					url:`/pages/index/personInfo/personInfo?id=${id}`
				})
			},
			async load(paging) {
				let res = await this.$u.api.lawCase.customerList({
					...paging,
					isMobile:1,
					type:this.value1,
					status:this.value2,
				})
				if (res) {
					res.page.list.map(item => {
						item.customer['type'] = item.customer['type']&&(this.vuex_dict.customer_type.find(dict => dict.value == item
							.customer.type))['label']
						item.customer['status'] = item.customer['status']&&(this.vuex_dict.cooperate_status.find(dict => dict.value ==
							item.customer.status))['label']
						item.customer['source'] = item.customer['source']&&(this.vuex_dict.customer_source.find(dict => dict.value ==
							item.customer.source))['label']
					})
					console.log(res, '+++++++++')
					this.list = [...this.list, ...res.page.list]
					this.$refs.list.loadSuccess({
						list: this.list,
						total: res.page.count
					});

				}
			},
			async refresh(paging = this.option) {
				this.list = []
				this.load(paging)
			}
		}
	}
</script>
<style lang="scss" scoped>
	.list {
		display: flex;
		flex-direction: column;
		height: 100%;
		box-sizing: border-box;

		// .view_list {
		// 	flex: 1;
		// 	overflow-y: auto;
		// }
	}
</style>
