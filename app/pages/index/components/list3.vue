<template>
	<view class="list">
		<!-- <view style="background-color: #FFFFFF;">
			<u-dropdown>
				<u-dropdown-item v-model="value1" :title="title1" :options="options1">
				</u-dropdown-item>
				<u-dropdown-item v-model="value2" :title="title2" :options="options2">
				</u-dropdown-item>
			</u-dropdown>
		</view>  -->
		<MyList ref="list" :option="option" @load="load" @refresh="refresh">
			<u-card @click="goPage(item.lawCase.id)" :title="item.typename" :sub-title="item.lawCase.createDate"  v-for="(item,i) in list" :key='i'>
				<view class="" slot="body">
				<view class="u-m-t-20">案件名称：{{item.lawCase.name}}</view>
				<view class="u-m-t-20">受理单位：{{item.lawCase.acceptUnitName}}</view>
				</view>
				<view class="u-flex u-row-between" slot="foot">
					<view class="u-line-1" style="width: 430rpx;">
						委托方：{{item.lawCase.entrustPersonNames}}
					</view>
					<view>
					<u-button size="mini" @click.stop="goPageInfo(item.lawCase.id,item.lawCase.name)">查看案件</u-button>
					</view>
				</view>  
			</u-card>
		</MyList>
		<block v-if="list.length<1">
			<u-empty text="暂无数据" mode="list"></u-empty>
		</block>

	</view>
</template>

<script>
	import MyList from '@/components/MyList.vue'
	export default {
		name: 'list1',
		components:{
			MyList
		},
		data() {
			return {
				list:[],
				option: {
				    pageNo: 1,
				    pageSize: 15,
				},
				value1: "",
				value2: '',
				options1: [{
						label: '全部',
						value: '',
					},{
						label: '共享他人',
						value: 1,
					},
					{
						label: '共享他人',
						value: 2,
					},
				],
				options2: [],
			}
		},
		computed:{
			title1(){
				return this.options1.find(item=>item.value==this.value1).label
			},
			title2(){
				if(this.options2.length<1){
					return ''
				}else{
					return this.options2.find(item=>item.value==this.value2).label
				}
			}
		},
		watch:{
			value1(){
				this.refresh()
			},
			value2(){
				this.refresh()
			},
		},
		created() {
			let arr = [{
						label: '全部',
						value: '',
					},]
			this.options2 = [...arr,...this.vuex_dict.case_audit_status]
			console.log(this.vuex_dict.case_status)
		},
		methods: {
			goPage(id){
				uni.navigateTo({
					url:`/pages/index/audit/audit?id=${id}`
				})
			},
			goPageInfo(id, name) {
				uni.navigateTo({
					url: `/pages/index/caseInfo/caseInfo?id=${id}&name=${name}`
				})
			},
			async load(paging){
				let res = await this.$u.api.lawCase.list({
					...paging,
					isMobile:1,
					checkWord:'',
					auditStatus:1,
				})
				if (res) {
					res.page.list.map(item=>{
						item['typename'] = (this.vuex_dict.case_type.find(dict=>dict.value==item.lawCase.type))['label']
					})
					console.log(res,'+++++++++')
					this.list = [...this.list, ...res.page.list]
					this.$refs.list.loadSuccess({ list: this.list, total: res.page.count });
					
				}
			},
			async refresh(paging=this.option){
				this.list = []
				this.load(paging)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.list {
		display: flex;
		flex-direction: column;
		height: 100%;
		box-sizing: border-box;

		// .view_list {
		// 	flex: 1;
		// 	overflow-y: auto;
		// }
	}
</style>
