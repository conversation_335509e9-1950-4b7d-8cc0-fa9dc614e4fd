<template>
	<view class="home">
		<view style="background-color: #FFFFFF;">
			<view style="width: 40%;">
				<u-tabs  :bar-height='10' :font-size="36" duration='0.1' :list="list" :is-scroll="false" bar-width='65' :current="current" @change="change"></u-tabs>
			</view>
		</view>
		<view class="u-flex-1" style="flex-shrink: 0;overflow-y: hidden;">
			<component :ref='list[current].list' :is="list[current].list"></component>
		</view>
		<view v-if="current!=2" class="pos" @click="goPage">
			+
		</view>
	</view> 
</template>

<script>
	import list1 from './components/list1.vue'
	import list2 from './components/list2.vue'
	import list3 from './components/list3.vue'
	export default {
		components:{
			list1,
			list2,
			list3,
		},
		data() {
			return {
				list: [{
					name: '案件',
					list:'list1'
				}, {
					name: '客户',
					list:'list2'
				},],
				current: 0,
			}
		},
		onShow() {  
			if(this.vuex_user.type.includes('1')){
				this.list = [{
					name: '案件',
					list:'list1'
				}, {
					name: '客户',
					list:'list2'
				}, {
					name: '审核',
					list:'list3'
				},]
			}else{
				this.list = [{
					name: '案件',
					list:'list1'
				}, {
					name: '客户',
					list:'list2'
				}]
			}
			if(this.$refs[this.list[this.current].list]){
				this.$refs[this.list[this.current].list].refresh()
			}
		},
		methods: {
			change(index) {
				this.current = index;
			},
			goPage(){
				let url = this.current == 1 ? './addPerson/addPerson':'./addCase/addCase'
				uni.navigateTo({
					url
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
/deep/ .u-tab-bar{
	bottom: -3px;
}
.home{
	display: flex;
	flex-direction: column;
	height: 100%;
	box-sizing: border-box;
	position: relative;
	overflow-y: hidden;
	.pos{
		$width:120rpx;
		position: absolute;
		width: $width;
		height: $width;
		background-color:$u-type-primary ;
		color: #FFFFFF;
		z-index: 99;
		bottom: 0;
		right: 0;
		margin: 60rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 70rpx;
		box-shadow: 10rpx 6rpx 16rpx #888888;
		/deep/ .u-btn{
			border-radius: 50%;
		}
	}
}
</style>
