<template>
	<view>
		<creatForm ref='creatForm' :formConfig='formConfig' @confirm='confirm' @submit='submit' />
	</view>
</template>

<script>
	import creatForm from '@/components/creatForm.vue'
	import formConfig from './formConfig.js'
	export default {
		components: {
			creatForm
		},
		data() {
			return {
				id:'',
				gid:'',
				formConfig: {},
				formObj: {},
				info:{},
			};
		},
		onLoad(e) {
			this.id = e.id
			this.gid = e.gid
			formConfig(this)
		},
		onShow() {
			let title = this.id?'编辑财产保全':'新增财产保全'
			uni.setNavigationBarTitle({
				title
			})
			if(this.id){
				this.casePropertyPreservationInfo()
			}
		},
		methods:{
			async casePropertyPreservationInfo(){
				let res = await this.$u.api.lawCase.casePropertyPreservationInfo(this.id)
				if(res){
					Object.assign(this.info,{
						...res.casePropertyPreservation,
						propertyType:this.vuex_dict.property_type.find(dict=>dict.value==res.casePropertyPreservation.propertyType)['label'],
						executeStatus:this.vuex_dict.property_execute_status.find(dict=>dict.value==res.casePropertyPreservation.executeStatus)['label'],
					})
					this.confirm({
						name:'propertyType',
						label: this.vuex_dict.property_type.find(dict=>dict.value==res.casePropertyPreservation.propertyType)['label'],
						value: this.vuex_dict.property_type.find(dict=>dict.value==res.casePropertyPreservation.propertyType)['value'],
					})
					this.confirm({
						name:'executeStatus',
						label: this.vuex_dict.property_execute_status.find(dict=>dict.value==res.casePropertyPreservation.executeStatus)['label'],
						value: this.vuex_dict.property_execute_status.find(dict=>dict.value==res.casePropertyPreservation.executeStatus)['value'],
					})
					this.formConfig[0].form.map(item=>{
						item['value'] = String(this.info[item.key])
						this.$refs['creatForm'].form[item.key] = String(this.info[item.key])
						return item
					})
				}
			},
			confirm(res) {
				this.formObj = {
					...this.formObj,
					[res['name']]: {
						id: res.value,
						name: res.label,
					}
				}
			},
			async submit(data){
				let res = this.$u.deepClone(data);
				let o = [
					'propertyType',
					'executeStatus',
				]
				Object.keys(this.formObj).forEach((key)=>{
					if(o.includes(key)){
						res[key] = this.formObj[key]['id']
					}
				})
				res['lawCase'] = {
					id:this.gid
				}
				res['id'] = this.id
				console.log(res)
				let result = await this.$u.api.lawCase.saveCasePropertyPreservation(res)
				if(result){
					this.$u.toast('提交成功')
					uni.navigateBack()
				}
			
				
			}
		}
	}
</script>

<style scoped lang="scss">

</style>
