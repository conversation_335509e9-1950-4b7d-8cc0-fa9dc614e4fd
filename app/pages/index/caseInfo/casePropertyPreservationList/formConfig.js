const formConfig = function(vm) {
	return new Promise(async (resolve, rejict) => {
		let result = [{
			title: '财产保全',
			key: 'concernPersonList',
			type: 'Array',
			add: false,
			form: [{
					label: '申请人',
					key: 'applicant',
					name: 'applicant',
					value: '',
					type: 'input',
					config: {},
					rules: [{
						required: true,
						message: '请输入申请人',
						trigger: ['change', 'blur'],
					}]
				},
				{
					label: '被申请人',
					key: 'respondent',
					name: 'respondent',
					value: '',
					type: 'input',
					config: {},
					rules: [{
						required: true,
						message: '请输入被申请人',
						trigger: ['change', 'blur'],
					}]
				},
				{
					label: '财产类型',
					key: 'propertyType',
					name: 'propertyType',
					value: '',
					type: 'select',
					config: {
						mode: 'single-column',
						label: 'label',
						value: 'value'
					},
					list: vm.vuex_dict.property_type,
					rules: [{
						required: true,
						message: '请选择财产类型',
						trigger: ['change', 'blur'],
					}]
				},
				{
					label: '裁定书编号',
					key: 'rulingNumber',
					name: 'rulingNumber',
					value: '',
					type: 'input',
					config: {},
					rules: [{
						required: true,
						message: '请输入裁定书编号',
						trigger: ['change', 'blur'],
					}]
				},
				{
					label: '查封日期',
					key: 'seizureDate',
					name: 'seizureDate',
					value: '',
					type: 'picker',
					config: {
						mode: 'time',
						params: {
							year: true,
							month: true,
							day: true,
						},
					},
					list: [],
					rules: [{
						required: true,
						message: '请选择日期',
						trigger: ['change', 'blur'],
					}]
				},
				{
					label: '查封到期日',
					key: 'seizureExpirationDate',
					name: 'seizureExpirationDate',
					value: '',
					type: 'picker',
					config: {
						mode: 'time',
						params: {
							year: true,
							month: true,
							day: true,
						},
					},
					list: [],
					rules: [{
						required: true,
						message: '请选择日期',
						trigger: ['change', 'blur'],
					}]
				},
				{
					label: '续封提醒',
					key: 'continueRemindDate',
					name: 'continueRemindDate',
					value: '',
					type: 'picker',
					config: {
						mode: 'time',
						params: {
							year: true,
							month: true,
							day: true,
						},
					},
					list: [],
					rules: [{
						required: true,
						message: '请选择日期',
						trigger: ['change', 'blur'],
					}]
				},
				// {
				// 	label: '提醒方式',
				// 	key: 'propertyType',
				// 	name: 'propertyType',
				// 	value: '',
				// 	type: 'select',
				// 	config: {
				// 		mode: 'single-column',
				// 		label: 'label',
				// 		value: 'value'
				// 	},
				// 	list: vm.vuex_dict.property_type,
				// 	rules: [{
				// 		required: true,
				// 		message: '请选择提醒方式',
				// 		trigger: ['change', 'blur'],
				// 	}]
				// },
				{
					label: '受理单位',
					key: 'acceptUnit',
					name: 'acceptUnit',
					value: '',
					type: 'input',
					config: {},
					rules: [{
						required: true,
						message: '请输入受理单位',
						trigger: ['change', 'blur'],
					}]
				},
				{
					label: '承办人员',
					key: 'undertakePerson',
					name: 'undertakePerson',
					value: '',
					type: 'input',
					config: {},
					rules: [{
						required: true,
						message: '请输入承办人员',
						trigger: ['change', 'blur'],
					}]
				},
				{
					label: '保全金额',
					key: 'preservationMoney',
					name: 'preservationMoney',
					value: '',
					type: 'input',
					config: {
						rightText: '元',
						type: 'digit'
					},
					rules: [{
						required: true,
						message: '请输入保全金额',
						trigger: ['change', 'blur'],
					}]
				},
				{
					label: '执行状态',
					key: 'executeStatus',
					name: 'executeStatus',
					value: '',
					type: 'select',
					config: {
						mode: 'single-column',
						label: 'label',
						value: 'value'
					},
					list: vm.vuex_dict.property_execute_status,
					rules: [{
						required: true,
						message: '请选择执行状态',
						trigger: ['change', 'blur'],
					}]
				},
				{
					label: '备注',
					key: 'remarks',
					name: 'remarks',
					value: '',
					type: 'input',
					config: {},
					rules: [{
						required: false,
						message: '请输入备注',
						trigger: ['change', 'blur'],
					}]
				},
			]
		}]
		vm.$data.formConfig = result
		resolve(result)
	})
}

export default formConfig
