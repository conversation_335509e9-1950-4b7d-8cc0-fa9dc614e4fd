<template>
	<view>
		<creatForm ref='creatForm' :formConfig='formConfig' @confirm='confirm' @submit='submit'>
			<view>
				<view  style="padding: 30rpx 0;">
					<u-cell-item title="上传附件:" hover-class='none' @click='upload' :arrow="false" :border-bottom='false'>
						<view slot="right-icon">
							<u-icon name="plus" size="35"></u-icon>
						</view>
					</u-cell-item>
				</view>
				<view  style="padding: 30rpx 0;" v-for="(item,key) in fileList" :key='key'>
					<u-cell-item :title="item.name" hover-class='none' :arrow="false" :border-bottom='false' @click="open(item.fullPath,item.fileType)">
							<view slot="icon">
								<image v-if="imgType.includes(item.fileType)" style="width: 80rpx;height: 80rpx;padding-right: 15rpx;"
									:src="item.fullPath" mode=""></image>
								<image v-if="docType.includes(item.fileType)" style="width: 80rpx;height: 80rpx;padding-right: 15rpx;"
									src="../../../../static/img/wd.png" mode=""></image>
							</view>
						<view slot="right-icon" @click.stop="delFile(key)">
							<u-icon name="close" color="red" size="35"></u-icon>
						</view>
					</u-cell-item>
				</view>
			</view>
				
		</creatForm>
	</view>
</template>

<script>
	import creatForm from '@/components/creatForm.vue'
	import formConfig from './formConfig.js'
	import httpUrl from '@/utils/config.js'
	export default {
		components: {
			creatForm
		},
		data() {
			return {
				id:'',
				gid:'',
				parent:'',
				formConfig: {},
				formObj: {},
				info:{},
				fileList:[],
				imgType: ['png', 'jpg', 'jpeg'],
				docType: ['docx', 'doc'],
			};
		},
		onLoad(e) {
			this.id = e.id
			this.gid = e.gid
			this.parent = e.parent
			formConfig(this)
			let title = this.id?'修改记录':'添加记录'
			uni.setNavigationBarTitle({
				title
			})
			this.caseStageList()
			// this.formConfig[0].form['sort']='1'
			// this.$refs['creatForm'].form['sort'] = '1'
			if(this.id){
				this.TodoInfo()
			}
		},
		onShow() {
			
		},
		methods:{
			getFileType(name){
				var fileName = name.lastIndexOf(".");//取到文件名开始到最后一个点的长度
				 
				var fileNameLength = name.length;//取到文件名长度
				 
				var fileFormat = name.substring(fileName + 1, fileNameLength);
				console.log(fileFormat)
				return fileFormat
			},
			delFile(key){
				this.fileList.splice(key,1)
			},
			upload() {
				uni.chooseImage({
					count: 1, // 最多可以选择的图片张数，默认9
					sizeType: ['compressed'], // original 原图，compressed 压缩图，默认二者都有
					sourceType: ['album', 'camera'], // album 从相册选图，camera 使用相机，默认二者都有
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						uni.uploadFile({
							url: httpUrl + '/law/sys/file/webupload/upload', //仅为示例，非真实的接口地址
							filePath: tempFilePaths[0],
							name: 'file',
							header: {
								token: this.vuex_token
							},
							formData: {
								'uploadPath': '/lawcase/todoFile/',
							},
							success: (uploadFileRes) => {
								this.$u.toast('上传成功')
								let res = JSON.parse(uploadFileRes.data)
								this.fileList.push({
									name:res.name,
									path:res.url,
									fileType:this.getFileType(res.name),
									fullPath:`http://oa.lgy0999.com/law`+res.url
								})
								console.log(res);
							}
						});
					}
				})
			},
			open(path, type) {
				console.log(path, type)
				if (this.imgType.includes(type)) {
					var urls = [path]
					uni.previewImage({
						current: 0,
						urls //预览图片的地址，必须要数组形式，如果不是数组形式就转换成数组形式就可以
					})
				} else {
					uni.downloadFile({
						url: path,
						success: (res) => {
							if (res.statusCode === 200) {
								uni.openDocument({
									filePath: res.tempFilePath,
									// 如果文件名包含中文，建议使用escape(res.tempFilePath)转码，防止ios和安卓客户端导致的差异
									success: function(res) {
										console.log('打开文档成功');
									}
								});
							}
						}
					})
				}
			},
			// 案件阶段
			async caseStageList(){
				let res = await this.$u.api.lawCase.caseStageList(this.gid)
				if(res){
					this.formConfig[0].form[5].list = res.page.list
				}
			},
			async TodoInfo(){
				let res = await this.$u.api.lawCase.TodoInfo(this.id)
				if(res){
					Object.assign(this.info,{
						...res.todoInfo,
						stage:res.todoInfo.stage.name,
					})
					this.fileList = res.todoInfo.fileList
					this.confirm({
						name:'stage',
						label: res.todoInfo.stage.name,
						value: res.todoInfo.stage.id,
					})
					this.formConfig[0].form.map(item=>{
						item['value'] = String(this.info[item.key]||'')
						this.$refs['creatForm'].form[item.key] = String(this.info[item.key]||'')
						return item
					})
				}
			},
			confirm(res) {
				this.formObj = {
					...this.formObj,
					[res['name']]: {
						id: res.value,
						name: res.label,
					}
				}
			},
			async submit(data){
				let res = this.$u.deepClone(data);
				res['relevanceType'] = '2'
				res['relevanceId'] = this.gid
				res['id'] = this.id||''
				res['parent'] = this?.info?.parent?.id||''
				// res['parent'] = this.parent||''
				res['stage'] = this.formObj['stage']
				res['fileList'] = this.fileList
				console.log(res)
				let result = await this.$u.api.lawCase.saveTodoInfo(res)
				if(result){
					this.$u.toast('提交成功')
					uni.navigateBack()
				}
			
				
			}
		}
	}
</script>

<style scoped lang="scss">

</style>
