const formConfig = function(vm) {
	return new Promise(async (resolve, rejict) => {
		let result = [{
			title: '记录',
			key: 'concernPersonList',
			type: 'Array',
			add: false,
			form: [{
					label: '工作摘要',
					key: 'name',
					name: 'name',
					value: '',
					type: 'input',
					config: {},
					rules: [{
						required: true,
						message: '请输入工作摘要',
						trigger: ['change', 'blur'],
					}]
				},{
					label: '工作详情',
					key: 'content',
					name: 'content',
					value: '',
					type: 'input',
					config: {
						type:'textarea'
					},
					rules: [{
						// required: true,
						message: '请输入工作详情',
						trigger: ['change', 'blur'],
					}]
				},{
					label: '开始日期',
					key: 'startDate',
					name: 'startDate',
					value: '',
					type: 'picker',
					config: {
						mode: 'time',
						params: {
							year: true,
							month: true,
							day: true,
							hour: true,
							minute: true,
							second: true,
						},
					},
					list: [],
					rules: [{
						required: true,
						message: '请选择开始日期',
						trigger: ['change', 'blur'],
					}]
				},{
					label: '结束日期',
					key: 'endDate',
					name: 'endDate',
					value: '',
					type: 'picker',
					config: {
						mode: 'time',
						params: {
							year: true,
							month: true,
							day: true,
							hour: true,
							minute: true,
							second: true,
						},
					},
					list: [],
					rules: [{
						required: false,
						message: '请选择结束日期',
						trigger: ['change', 'blur'],
					}]
				},{
					label: '提醒时间',
					key: 'remindDate',
					name: 'remindDate',
					value: '',
					type: 'picker',
					config: {
						mode: 'time',
						params: {
							year: true,
							month: true,
							day: true,
							hour: true,
							minute: true,
							second: true,
						},
					},
					list: [],
					rules: [{
						// required: true,
						message: '请选择提醒时间',
						trigger: ['change', 'blur'],
					}]
				},
				{
					label: '案件阶段',
					key: 'stage',
					name: 'stage',
					value: '',
					type: 'select',
					config: {
						mode: 'single-column',
						label: 'name',
						value: 'id'
					},
					list: [],
					rules: [{
						required: true,
						message: '请选择案件阶段',
						trigger: ['change', 'blur'],
					}]
				},
				{
						label: '排序',
						key: 'sort',
						name: 'sort',
						value: '1',
						type: 'input',
						config: {
							type: 'number'
						},
						rules: [{
							required: true,
							message: '请输入排序',
							trigger: ['change', 'blur'],
						}]
					},
				
				
			]
		}]
		vm.$data.formConfig = result
		resolve(result)
	})
}

export default formConfig
