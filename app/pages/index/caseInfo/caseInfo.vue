<template>
	<view class="content" >
		<u-tabs :list="list" :is-scroll="false" :current="current" @change="change"></u-tabs>
		<view class="u-flex-1" style="overflow-y: auto;">
			<view class="u-border-top">
				<component :result='result' :id="id" :is="list[current].req" @getList='getData'></component>
			</view>
			<block v-if="current==1">
				<u-row gutter="16">
					<u-col span="10">
						<u-tabs :list="two.list" :is-scroll="false" :current="two.current" @change="twoChange"
							bg-color='transparent'></u-tabs>
					</u-col>
					<u-col span="2">
						<u-button size="mini" shape="square" @click="twoAddPage">添加</u-button>
					</u-col>
				</u-row>
				<view class="u-border-top" v-if="two.show">
					<component :result='two.result' :id="id" :is="two.list[two.current].req" @getList='twoGetData'>
					</component>
				</view>
			</block>
		</view>
	</view>
</template>

<script>
	import detailInfo from './components/info.vue'
	import toAcceptSituation from './components/toAcceptSituation.vue'
	import financeFlowRecord from './components/financeFlowRecord.vue'
	import caseDocuments from './components/caseDocuments.vue'
	import property from './components/result/property.vue'
	import courtRecords from './components/result/courtRecords.vue'
	import implementationOf from './components/result/implementationOf.vue'
	export default {
		components: {
			detailInfo,
			toAcceptSituation,
			financeFlowRecord,
			caseDocuments,
			property,
			courtRecords,
			implementationOf,
		},
		data() {
			return {
				id: '',
				current: 0,
				result: {},
				list: [{
						name: '基础信息',
						list: 'detailInfo',
						req: 'detailInfo',
					}, {
						name: '受理情况',
						req: 'toAcceptSituation',
						list: 'detailInfo'
					},{
						name: '财务流水',
						req: 'financeFlowRecord',
						list: 'listByCase'
					},
					{
						name: '案件文档',
						req: 'caseDocuments',
						list: 'fileData'
					},
					// {
					// 	name: '案件文档',
					// 	req:'detailInfo',
					// 	list:'detailInfo'
					// },
				],
				two: {
					show:true,
					current: 0,
					result: [],
					list: [{
						name: '财产保全',
						req: 'property',
						list: 'casePropertyPreservationList',
					}, {
						name: '庭审记录',
						req: 'courtRecords',
						list: 'caseTrialRecordList'
					}, {
						name: '执行情况',
						req: 'implementationOf',
						list: 'caseExecuteSituationList'
					}, ]
				}
			}
		},
		computed: {
			mtype() {
				return this.list[this.current].list
			},
			twoMtype() {
				return this.two.list[this.two.current].list
			},
		},
		onLoad(e) {
			this.id = e.id
		},
		onShow() {
			console.log(1212132)
			this.getData()
			//底部列表
			if (this.current == 1) {
				this.twoGetData()
			}
		},
		methods: {
			change(index) {
				this.current = index;
				this.getData()
				if (this.current == 1) {
					this.twoGetData()
				}
			},
			async getData() {
				let res = await this.$u.api.lawCase[this.mtype](this.id)
				if (res) {
					if (res.data) {
						this.result = res.data
					} else if(res.directoryData){
						this.result = res
						// this.result = {
						// 	directoryData:res.directoryData.map(item=>item['show']=false),
						// 	fileData:res.fileData.map(item=>item['show']=false),
						// }
					}else {
						this.result = res.page
					}
					console.log(res, 55555555)
				}
			},
			twoAddPage(){
				uni.navigateTo({
					url:`./${this.twoMtype}/${this.twoMtype}?gid=${this.id}`
				})
			},
			twoChange(index) {
				this.two.current = index;
				this.twoGetData()
			},
			async twoGetData() {
				this.two.show = false
				let res = await this.$u.api.lawCase[this.twoMtype](this.id)
				if (res) {
					if (res.data) {
						this.two.result = res.data
					} else {
						this.two.result = res.page
					}
					this.two.show = true
					console.log(res, 55555555)
				}
			},
		}
	}
</script>
<style scoped lang="scss">
	.person_box {
		margin: 30rpx;
		display: flex;

		.add {
			text-align: center;
			margin: 0 auto;
			width: 70rpx;
			line-height: 35rpx;
			padding: 10rpx 16rpx;
			border: 1px dashed #ccc;
			background-color: #FFFFFF;
		}

		.p_list {
			overflow-x: auto;
			display: flex;
			padding: 10rpx;

			.p_item {
				box-shadow: 10rpx 10rpx 5rpx 0 rgba(190, 190, 190, 0.75);
				background-color: #FFFFFF;
				border-radius: 10rpx;
				width: 200rpx;
				height: 100%;
				flex-shrink: 0;
				margin-right: 35rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;
				font-weight: bold;
				font-size: 35rpx;

				.tag {
					position: absolute;
					top: 0;
					left: 0;
					font-size: 23rpx;
					text-align: center;
					width: 100rpx;
					padding: 5rpx;
					border-top-right-radius: 10rpx;
					border-bottom-left-radius: 10rpx;
					color: $u-type-primary-dark;
					background: $u-type-primary-light;
				}
			}
		}
	}
</style>
