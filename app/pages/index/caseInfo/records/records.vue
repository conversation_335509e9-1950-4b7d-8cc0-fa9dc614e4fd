<template>
	<view>
		<u-cell-group>
				<u-cell-item title="工作摘要" :value="info.name" :arrow="false"></u-cell-item>
				<u-cell-item title="工作详情" :value="info.content" title-width='200' :arrow="false"></u-cell-item>
				<u-cell-item title="开始日期" :value="info.startDate" :arrow="false"></u-cell-item>
				<u-cell-item title="结束日期" :value="info.endDate" :arrow="false"></u-cell-item>
				<u-cell-item title="提醒时间" :value="info.remindDate" :arrow="false"></u-cell-item>
				<u-cell-item v-if="info.stage" title="案件阶段" :value="info.stage.name" :arrow="false"></u-cell-item>
				<u-cell-item title="排序" :value="info.sort" :arrow="false"></u-cell-item>
			</u-cell-group>
			<view  style="padding: 30rpx 0;" v-for="(item,key) in info.fileList" :key='key'>
				<u-cell-item :title="item.name" hover-class='none' :arrow="false" :border-bottom='false' @click="open(item.fullPath,item.fileType)">
						<view slot="icon">
							<image v-if="imgType.includes(item.fileType)" style="width: 80rpx;height: 80rpx;padding-right: 15rpx;"
								:src="item.fullPath" mode=""></image>
							<image v-if="docType.includes(item.fileType)" style="width: 80rpx;height: 80rpx;padding-right: 15rpx;"
								src="../../../../static/img/wd.png" mode=""></image>
						</view>
				</u-cell-item>
			</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				info:{},
				imgType: ['png', 'jpg', 'jpeg'],
				docType: ['docx', 'doc'],
			};
		},
		onLoad(e) {
			this.id = e.id
			this.TodoInfo()
		},
		methods:{
			open(path, type) {
				console.log(path, type)
				if (this.imgType.includes(type)) {
					var urls = [path]
					uni.previewImage({
						current: 0,
						urls //预览图片的地址，必须要数组形式，如果不是数组形式就转换成数组形式就可以
					})
				} else {
					uni.downloadFile({
						url: path,
						success: (res) => {
							if (res.statusCode === 200) {
								uni.openDocument({
									filePath: res.tempFilePath,
									// 如果文件名包含中文，建议使用escape(res.tempFilePath)转码，防止ios和安卓客户端导致的差异
									success: function(res) {
										console.log('打开文档成功');
									}
								});
							}
						}
					})
				}
			},
			async TodoInfo(){
				let res = await this.$u.api.lawCase.TodoInfo(this.id)
				if(res){
					this.info = res.todoInfo
				}
			},
		}
	}
</script>

<style lang="scss">

</style>
