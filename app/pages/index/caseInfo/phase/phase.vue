<template>
	<view>
		<view class="u-flex u-row-between u-padding-25">
			<text class="u-font-35" style="font-weight: bold;">阶段管理</text>
			<text style="color: #999;" @click="show=true">添加阶段</text>
		</view>
		<view class="u-padding-left-25 u-padding-bottom-25" style="color: #999;">将案件记录分阶段管理，可设置案件当前阶段</view>
		<u-cell-group>
			<block v-for="(item,key) in list" :key='key'>
				<u-cell-item :icon="item.isCurrent==='1'?'star-fill':'star'" :arrow="false" @click='showS(item.id)' :title="item.name" >
					<view  slot='right-icon'>
						<u-icon name="more-dot-fill"></u-icon>
					</view>
				</u-cell-item>
			</block>
		</u-cell-group>
		<u-modal async-close ref='mode' v-model="show" title='添加阶段' show-cancel-button confirm-text='保存' @confirm='confirm'>
			<view class="slot-content u-padding-25">
				<u-input v-model="value"  :border="true" />
			</view>
		</u-modal>
		<u-action-sheet :list="clist" @click="click" v-model="cshow"></u-action-sheet>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id:'',
				cid:'',
				show:false,
				value:'',
				list:[],
				clist:[ {
					text: '设为当前阶段'
				}, {
					text: '删除' ,
					color: 'red',
				}],
				cshow:false,
			};
		},
		onLoad(e) {
			this.id = e.id
		},
		onShow() {
			this.getList()
		},
		methods:{
			click(type){
				switch(type){
					case 0:
					this.caseStageSetCurrent()
					break;
					case 1:
					this.delCaseStage()
					break;
				}
			},
			showS(id){
				this.cid = id
				this.cshow = true
			},
			async confirm(){
				this.$refs.mode.clearLoading()
				if(this.value){
					let res = await this.$u.api.lawCase.saveCaseStage({
						'lawCase.id':this.id,
						name: this.value
					})
					if(res){
						this.getList()
						this.show = false
						this.value = ''
					}
				}else{
					this.$u.toast('内容不能为空！')
				}
			},
			async getList(){
				let res = await this.$u.api.lawCase.caseStageList(this.id)
				if(res){
					this.list = res.page.list
				}
			},
			//设为当前阶段
			async caseStageSetCurrent(){
				let res = await this.$u.api.lawCase.caseStageSetCurrent(this.cid)
				if(res){
					this.$u.toast('操作成功')
					this.getList()
				}
			},
			async delCaseStage(){
				let res = await this.$u.api.lawCase.delCaseStage(this.cid)
				if(res){
					this.$u.toast('删除成功')
					this.getList()
				}
			}
		}
	}
</script>

<style lang="scss">

</style>
