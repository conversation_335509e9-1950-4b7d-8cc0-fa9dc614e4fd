<template>
	<view>
		<creatForm ref='creatForm' :formConfig='formConfig' @confirm='confirm' @submit='submit' />
	</view>
</template>

<script>
	import creatForm from '@/components/creatForm.vue'
	import formConfig from './formConfig.js'
	export default {
		components: {
			creatForm
		},
		data() {
			return {
				id:'',
				formConfig: {},
				formObj: {}
			};
		},
		onLoad(e) {
			this.id = e.id
			formConfig(this)
		},
		methods:{
			async submit(data){
				let res = this.$u.deepClone(data);
				res['lawCase.id'] = this.id
				let result = await this.$u.api.lawCase.saveCaseUndertakePerson(res)
				if(result){
					this.$u.toast('提交成功')
					uni.navigateBack({
						delta:2
					})
				}
			}
		}
	}
</script>

<style scoped lang="scss">

</style>
