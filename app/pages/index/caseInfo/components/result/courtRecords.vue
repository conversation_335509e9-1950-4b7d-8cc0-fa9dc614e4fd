<template>
	<view class="content" v-if="list">
		<u-modal v-model="show" content="是否删除当前数据？" show-cancel-button @confirm="delCaseTrialRecord"></u-modal>
		<block v-for="(item,key) in list" :key='key'>
			
			<u-card margin="30rpx"  @click="goPage(item)"
				:title="vuex_dict.open_court_type.find(dict=>dict.value==item.openCourtType)['label']"
				>
				<view class="" slot="body">
					<u-row gutter="16" @click="goPage(item)" style="min-width: 600rpx;">
						<u-col span="12">
							<view class="u-m-b-20">出庭律师：{{item.barrister.name}}</view>
						</u-col>
						<u-col span="12">
							<view class="u-m-b-20">开庭地址：{{item.openCourtAddress}}</view>
						</u-col>
					</u-row>
				</view>
				<view slot='foot' class="u-flex u-row-between">
					<view>
						开庭日期：{{item.openCourtDate}}
					</view>
					<view @tap.stop="click(item.id)">
						<u-icon name="trash-fill" size="34" color="red" label-color='red' label="删除"></u-icon>
					</view>
				</view>
			</u-card>
		</block>
		<view class="u-margin-top-50" v-if="list.length<1">
			<u-empty text="暂无数据" mode="list"></u-empty>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			result: {
				type: Object | Array,
			},
			id: {
				type: String
			}
		},
		data() {
			return {
				show:false,
				ids:'',
				options: [{
					text: '删除',
					style: {
						backgroundColor: '#dd524d'
					}
				}]
			}
		},
		computed: {
			list() {
				return this.result.list
			}
		},
		methods: {
			click(index) {
				this.ids = index
				this.show = true
				console.log(index)
			},
			async delCaseTrialRecord(){
				console.log(this.ids,'del')
				let result = await this.$u.api.lawCase.delCaseTrialRecord(this.ids)
				if(result){
					this.$u.toast('删除成功')
					this.$emit('getList')
				}
			},
			goPage(item) {
				uni.navigateTo({
					url: `./caseTrialRecordList/caseTrialRecordList?id=${item.id}&gid=${item.lawCase.id}`
				})
			},
		}
	}
</script>

<style>
</style>
