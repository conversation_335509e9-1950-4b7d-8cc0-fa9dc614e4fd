<template>
	<view class="content" v-if="list">
		<u-modal v-model="show" content="是否删除当前数据？" show-cancel-button @confirm="delCasePropertyPreservation"></u-modal>
		<block v-for="(item,key) in list" :key='key'>
			
			<u-card margin="30rpx"  @click="goPage(item)"
				:title="vuex_dict.property_type.find(dict=>dict.value==item.propertyType)['label']"
				:sub-title="item.executeStatus&&vuex_dict.property_execute_status.find(dict=>dict.value==item.executeStatus)['label']">
				<view class="" slot="body">
					<u-row gutter="16" @click="goPage(item)" style="min-width: 600rpx;">
						<u-col span="6">
							<view class="u-m-b-20">申请人：{{item.applicant}}</view>
						</u-col>
						<u-col span="6">
							<view class="u-m-b-20">被申请人：{{item.respondent}}</view>
						</u-col>
						<u-col span="6">
							<view class="u-m-b-20">保全金额：{{item.preservationMoney}}</view>
						</u-col>
						<u-col span="6" class=" u-line-1">
							<view class="u-m-b-20">受理单位：{{item.acceptUnit}}</view>
						</u-col>
					</u-row>
				</view>
				<view slot='foot' class="u-flex u-row-between">
					<view>
						查封到期日：{{item.seizureExpirationDate}}
					</view>
					<view @tap.stop="click(item.id)">
						<u-icon name="trash-fill" size="34" color="red" label-color='red' label="删除"></u-icon>
					</view>
				</view>
			</u-card>
		</block>
		<view class="u-margin-top-50" v-if="list.length<1">
			<u-empty text="暂无数据" mode="list"></u-empty>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			result: {
				type: Object | Array,
			},
			id: {
				type: String
			}
		},
		data() {
			return {
				show:false,
				ids:'',
			}
		},
		computed: {
			list() {
				return this.result.list
			}
		},
		methods: {
			click(index) {
				this.ids = index
				this.show = true
				console.log(index)
			},
			async delCasePropertyPreservation(){
				console.log(this.ids,'del')
				let result = await this.$u.api.lawCase.delCasePropertyPreservation(this.ids)
				if(result){
					this.$u.toast('删除成功')
					this.$emit('getList')
				}
			},
			goPage(item) {
				uni.navigateTo({
					url: `./casePropertyPreservationList/casePropertyPreservationList?id=${item.id}&gid=${item.lawCase.id}`
				})
			},
		}
	}
</script>

<style>
</style>
