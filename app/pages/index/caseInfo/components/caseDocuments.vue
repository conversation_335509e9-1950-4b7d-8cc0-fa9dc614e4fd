<template>
	<view>
		<u-sticky offset-top='80'>
			<view class="u-padding-30 u-flex u-row-between" style="background-color: #f5f5f5;">
				<view class="t">案件文书</view>
				<u-icon name="plus" size="45" @click="show = true"></u-icon>
			</view>
		</u-sticky>
		<u-cell-group>
			<block v-if="result.directoryData">
				<u-swipe-action  :index="item.id" v-for="(item,key) in result.directoryData" :key='item.id'
					@click="delDir" :options="options">
					<view @click="openChild(item)" style="width: 100vw;">
						<u-cell-item :title="item.name">
							<template #icon>
								<image style="width: 80rpx;height: 80rpx;" src="../../../../static/img/wj.png" mode="">
								</image>
							</template>
						</u-cell-item>
					</view>
				</u-swipe-action>
				
				<u-swipe-action  :index="item.id" v-for="(item,key) in result.fileData" :key='item.id'
					@click="delFile" :options="options">
					<view style="width: 100vw;">
						<u-cell-item :arrow="false" :title="item.name" @click="open(item.path,item.fileType)">
							<template #icon>
								<view>
									<image v-if="imgType.includes(item.fileType)" style="width: 80rpx;height: 80rpx;"
										:src="item.path" mode=""></image>
									<image v-if="docType.includes(item.fileType)" style="width: 80rpx;height: 80rpx;"
										src="../../../../static/img/wd.png" mode=""></image>
								</view>
							</template>
						</u-cell-item>					
					</view>
				</u-swipe-action>
			</block>
			<u-action-sheet :z-index='9999999' :list="actionList" @click="click" v-model="show"></u-action-sheet>
			<u-popup v-model="status" mode="right">
				<view style="width: 100vw;">
					<u-sticky>
						<!-- 只能有一个根元素 -->
						<view class="sticky">
							<u-navbar back-text="关闭" :title="info.name" :custom-back='back'>
								<template v-slot:right>
									<view class="u-padding-right-30">
										<u-icon name="plus" size="45" @click="show = true"></u-icon>
									</view>
								</template>
							</u-navbar>
						</view>
					</u-sticky>
					<block  v-if="detail.directoryData">
						<u-swipe-action  :index="item.id" v-for="(item,key) in detail.directoryData" :key='item.id'
							@click="delDir" :options="options">
							<view @click="openChild(item)" style="width: 100vw;">
								<u-cell-item :title="item.name">
									<template #icon>
										<image style="width: 80rpx;height: 80rpx;" src="../../../../static/img/wj.png" mode="">
										</image>
									</template>
								</u-cell-item>
							</view>
						</u-swipe-action>
						
						<u-swipe-action  :index="item.id" v-for="(item,key) in detail.fileData" :key='item.id'
							@click="delFile" :options="options">
							<view style="width: 100vw;">
								<u-cell-item :arrow="false" :title="item.name" @click="open(item.path,item.fileType)">
									<template #icon>
										<view>
											<image v-if="imgType.includes(item.fileType)" style="width: 80rpx;height: 80rpx;"
												:src="item.path" mode=""></image>
											<image v-if="docType.includes(item.fileType)" style="width: 80rpx;height: 80rpx;"
												src="../../../../static/img/wd.png" mode=""></image>
										</view>
									</template>
								</u-cell-item>							
							</view>
						</u-swipe-action>						
					</block>
				</view>
			</u-popup>
			<u-modal async-close ref='mode' v-model="dirShow" title='新建文件夹' show-cancel-button confirm-text='保存'
				@confirm='createDir'>
				<view class="slot-content u-padding-25">
					<u-input v-model="dirName" :border="true" />
				</view>
			</u-modal>
		</u-cell-group>
	</view>
</template>

<script>
	import httpUrl from '@/utils/config.js'
	export default {
		props: {
			result: {
				type: Object | Array,
			},
			id: {
				type: String
			},
			date: {
				type: String
			}
		},
		data() {
			return {
				options: [{
					text: '删除',
					style: {
						backgroundColor: '#dd524d'
					}
				}],
				actionList: [{
					text: '上传文件'
				}, {
					text: '新建文件夹'
				}],
				show: false,
				dirShow: false,
				status: false,
				res: {},
				imgType: ['png', 'jpg', 'jpeg'],
				docType: ['docx', 'doc'],
				info: {
					id: ''
				},
				detail: {},
				dirName: '',
			}
		},
		watch: {
			dirShow() {
				this.dirName = ''
			},
			result(n) {
				// this.res = {
				// 	directoryData: n.directoryData.map(item => {
				// 		item['show'] = false
				// 		return item
				// 	}),
				// 	fileData: n.fileData.map(item => {
				// 		item['show'] = false
				// 		return item
				// 	}),
				// }
				// this.res = n
			}
		},
		mounted() {},
		methods: {
			delDir(e, a) {
				this.delCaseFileDirectory(e)
				console.log(e, a)
			},
			delFile(e,a){
				this.delCaseFile(e)
			},
			async delCaseFileDirectory(ids){
				let res = await this.$u.api.lawCase.delCaseFileDirectory(ids)
				if(res){
					this.$u.toast('删除成功')
					if (this.detail.fileData) {
						this.getList()
					} else {
						this.$emit('getList')
					}
				}
			},
			async delCaseFile(ids){
				let res = await this.$u.api.lawCase.delCaseFile(ids)
				if(res){
					this.$u.toast('删除成功')
					if (this.detail.fileData) {
						this.getList()
					} else {
						this.$emit('getList')
					}
				}
			},
			async createDir() {
				this.$refs.mode.clearLoading()
				let res = await this.$u.api.lawCase.caseFileDirectory({
					'lawCase.id': this.id,
					'name': this.dirName,
					'parent.id': this.info.id,
				})
				if (res) {
					if (this.detail.fileData) {
						this.getList()
					} else {
						this.$emit('getList')
					}
					this.dirShow = false
				}
			},
			back() {
				console.log(11)
				this.info = {
					id: ''
				}
				this.detail = {}
				this.status = false
			},
			openChild(item) {
				this.info = item
				this.status = true
				this.getList()
			},
			async getList() {
				let res = await this.$u.api.lawCase.fileData(this.id, this.info.id)
				if (res) {
					this.detail = res
				}
			},
			click(e) {
				if (e == 0) {
					this.upload()
				} else {
					this.dirShow = true
				}
			},
			upload() {
				uni.chooseImage({
					count: 1, // 最多可以选择的图片张数，默认9
					sizeType: ['compressed'], // original 原图，compressed 压缩图，默认二者都有
					sourceType: ['album', 'camera'], // album 从相册选图，camera 使用相机，默认二者都有
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						uni.uploadFile({
							url: httpUrl + 'law/lawcase/caseFile/upload', //仅为示例，非真实的接口地址
							filePath: tempFilePaths[0],
							name: 'file',
							header: {
								token: this.vuex_token
							},
							formData: {
								'lawCase.id': this.id,
								'fileDirectory.id': this.info.id,
							},
							success: (uploadFileRes) => {
								this.$u.toast('上传成功')
								if (this.detail.fileData) {
									this.getList()
								} else {
									this.$emit('getList')
								}
								console.log(uploadFileRes.data);
							}
						});
					}
				})
			},

			open(path, type) {
				console.log(path, type)
				if (this.imgType.includes(type)) {
					var urls = [path]
					uni.previewImage({
						current: 0,
						urls //预览图片的地址，必须要数组形式，如果不是数组形式就转换成数组形式就可以
					})
				} else {
					uni.downloadFile({
						url: path,
						success: (res) => {
							if (res.statusCode === 200) {
								uni.openDocument({
									filePath: res.tempFilePath,
									// 如果文件名包含中文，建议使用escape(res.tempFilePath)转码，防止ios和安卓客户端导致的差异
									success: function(res) {
										console.log('打开文档成功');
									}
								});
							}
						}
					})
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.t {
		font-size: 35rpx;
	}

	/deep/.u-cell_title {
		padding-left: 30rpx;
	}
</style>
