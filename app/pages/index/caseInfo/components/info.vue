<template>
	<view class="content">
		<view class="u-flex-1" style="overflow-y: auto;" v-if="result.lawCase">
			<!-- 基本信息 -->
			<view class="">
				<u-card margin="30rpx" @click="goPage(`./info/info?id=${id}`)"
					:title="vuex_dict.case_type.find(dict=>dict.value==result.lawCase.type)['label']">
					<view class="" slot="body">
						<view class="u-m-b-20">案由：{{result.lawCase.caseCause.name}}</view>
						<view>程序：{{result.lawCase.caseProgram.name}}</view>
					</view>
				</u-card>
				<view class="person_box">
					<view class="add u-m-r-20" @click="goPage(`./addLitigant/addLitigant?id=${id}`)">
						添加当事人
					</view>
					<view class="u-flex-1 p_list">
						<view @click="goPage(`./litigantInfo/litigantInfo?id=${item.id}&gid=${item.lawCase.id}`)"
							class="p_item" v-for="(item,key) in result.concernPersonList" :key='key'>
							<view class="tag">
								{{item.isEntrust==0?'被告':'委托人'}}
							</view>
							<view style="width: 80%;" class="u-line-1 u-text-center">
								{{item.name}}
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="u-margin-30">
				<view class="u-flex u-row-between u-margin-bottom-30" style="align-items: center;">
					<view class="bold" style="font-weight: bold;" @click="show=true">
						<text class="u-margin-right-10 u-font-30">全部记录</text>
						<u-icon name="arrow-down-fill"></u-icon>
					</view>
					<view>
						<u-button size="mini" @click="goPage(`./addRecords/addRecords?gid=${id}`)">添加</u-button>
					</view>
				</view>
				<leo-tree :data="caseList" :defaultProps='defaultProps'></leo-tree>
				<!-- <u-cell-group>
						<u-cell-item 
						:style="{background:item.status==2?'#F3FEF3':'#FDF4F4'}"
						:arrow="false"
						v-for="(item,index) in caseList"  
						:title="item.name"
						@click="statusFun(item.id,item.name,item)"
						>
							<view slot="right-icon" @click.stop="actionShowFun(`./addRecords/addRecords?gid=${id}&id=${item.id}`,item)">
								<u-icon name="more-dot-fill"  size="35"></u-icon>
							</view>
						</u-cell-item>
					</u-cell-group> -->
				<view class="u-padding-top-30" v-if="caseList.length<1">
					<u-empty text="暂无数据" mode="list"></u-empty>
				</view>
			</view>
		</view>
		<u-modal v-model="show" :show-title='false' :show-confirm-button='false'>
			<view class="model_box">
				<view class="u-flex u-row-between u-padding-25 ">
					<view class="u-font-28 text" @click="show=false">取消</view>
					<view style="font-weight: bold;">选择阶段记录</view>
					<view class="u-font-28 text" @click="sub">完成</view>
				</view>
				<view class="u-flex-1" style="overflow-y: auto;">
					<view class="u-padding-30">
						<u-radio-group wrap v-model="value">
							<u-radio @change="radioChange" v-for="(item, index) in list" :key="index" :name="item.id">
								<view class="u-padding-10">
									{{item.name}}
								</view>
							</u-radio>
						</u-radio-group>

					</view>
				</view>
				<view class="bottom u-flex u-row-center" @click="goPage(`./phase/phase?id=${id}`)">
					<u-icon name="setting" color="#A0A6BA" size="38"></u-icon>
					<text class="u-margin-left-10">管理阶段</text>
				</view>
			</view>
		</u-modal>

		<u-action-sheet :z-index='9999999' :list="actionList" @click="click" v-model="actionShow"></u-action-sheet>
		<u-popup v-model="status" mode="right">
			<view style="width: 100vw;">
				<u-sticky>
					<!-- 只能有一个根元素 -->
					<view class="sticky">
						<u-navbar back-text="关闭" :title="name" :custom-back='back'>
							<template v-slot:right>
								<view class="u-padding-right-30">
									<u-icon name="plus" size="45"
										@click="goPage(`./addRecords/addRecords?gid=${id}&parent=${actionInfo.id}`)">
									</u-icon>
								</view>
							</template>
						</u-navbar>
					</view>
				</u-sticky>
				<u-cell-group>
					<u-cell-item :style="{background:item.status==2?'#F3FEF3':'#FDF4F4'}" :arrow="false"
						v-for="(item,index) in childList" :title="item.name" @click="statusFun(item.id,item.name,item)">
						<!-- @click="goPage(`./addRecords/addRecords?gid=${id}&id=${item.id}`)" -->
						<view slot="right-icon"
							@click.stop="actionShowFun(`./addRecords/addRecords?gid=${id}&id=${item.id}&parent=${actionInfo.id}`,item)">
							<u-icon name="more-dot-fill" size="35"></u-icon>
						</view>
					</u-cell-item>
				</u-cell-group>
				<view class="u-padding-top-30" v-if="childList.length<1">
					<u-empty text="暂无数据" mode="list"></u-empty>
				</view>

			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		props: {
			result: {
				type: Object | Array,
			},
			id: {
				type: String
			},
			date: {
				type: String
			}
		},
		data() {
			return {
				defaultProps: {
					id: 'id',
					label: 'name',
					children: 'children'
				},
				data: [{
						"name": "一级架构",
						"id": "1",
						"open": false,
						"children": [{
								"name": "二级架构",
								"id": "1-1",
								"open": false,
								"file": [{
										"name": "1.txt",
										"fileid": "41245654"
									},
									{
										"name": "2.txt",
										"fileid": "78545562"
									}
								],
								"children": [{
									"name": "三级架构",
									"id": "1-1-1",
									"open": false,
									"children": [],
									"file": [{
										"name": "3.txt",
										"fileid": "8565788"
									}]
								}]
							},
							{
								"name": "二级架构",
								"id": "1-2",
								"open": false,
								"children": [{
									"name": "三级架构",
									"id": "1-2-1",
									"open": false,
									"children": [],
									"file": [{
										"name": "xxx.txt",
										"fileid": "13454574"
									}]
								}]
							}
						]
					},
					{
						"name": "一级架构",
						"id": "2",
						"open": false,
						"children": [{
							"name": "二级架构",
							"id": "2-1",
							"open": false,
							"children": []
						}]
					},
					{
						"name": "一级架构",
						"id": "3",
						"open": false,
						"children": []
					},
					{
						"name": "一级架构",
						"id": "4",
						"open": false,
						"children": []
					}
				],
				url: '',
				name: '',
				show: false,
				actionShow: false,
				status: false,
				list: [],
				caseList: [],
				childList: [],
				actionInfo: {
					id: ''
				},
				value: '',
				actionList: [{
					text: '更改状态',
					disabled: false
				}, {
					text: '查看',
					disabled: false
				}, {
					text: '编辑',
					disabled: false
				}, {
					text: '删除',
					disabled: false
				}],
			}
		},
		mounted() {
			this.caseData()
		},
		watch: {
			result(n) {
				this.caseData()
				this.caseDataChild(this.actionInfo.id)
				setTimeout(() => {
					this.show = false
					this.actionShow = false
					this.status = false
				}, 10)

			},
			date() {
				this.caseData()
			},
			show(n, o) {
				if (n) {
					this.caseStageList()
				} else {
					this.caseData()
				}
			}
		},
		methods: {
			async delTodoInfo() {
				let res = await this.$u.api.lawCase.delTodoInfo(this.actionInfo.id)
				if (res) {
					this.caseData()
					this.status = false
				}
			},
			statusFun(id, name, actionInfo) {
				this.actionInfo = actionInfo
				this.name = name
				this.caseDataChild(id)
				this.status = true
			},
			actionShowFun(url, actionInfo) {
				this.url = url
				this.actionInfo = actionInfo
				this.actionShow = true
			},
			click(e, c) {
				console.log(e, c)
				switch (e) {
					case 0:
						this.todoInfoStatus()
						break;
					case 1:
						this.goPage(`./records/records?id=${this.actionInfo.id}`)
						break;
					case 2:
						this.goPage(this.url)
						break;
					case 3:
						this.delTodoInfo()
						break;
				}
				console.log(e, c)
			},
			back() {
				this.status = false
			},
			async caseData(parentId = '') {
				if (this.result?.lawCase?.status == '0') {
					this.actionList.map(item => {
						item['disabled'] = true
					})
					this.actionList[1]['disabled'] = false
				} else {
					this.actionList.map(item => {
						item['disabled'] = false
					})
				}
				let res = await this.$u.api.calendar.caseData({
					relevanceId: this.id,
					'stage.id': this.value,
					// 'parent.id': parentId,
				})
				if (res) {
					this.caseList = res.list
					console.log(res.list,'+++++++++++++++++')
				}
			},
			async caseDataChild(parentId = '') {
				let res = await this.$u.api.calendar.caseData({
					relevanceId: this.id,
					'parent.id': parentId,
				})
				if (res) {
					this.childList = res.list
				}
			},
			async todoInfoStatus() {
				let res = await this.$u.api.lawCase.todoInfoStatus({
					id: this.actionInfo.id,
					status: this.actionInfo.status == 1 ? '2' : '1',
				})
				if (res) {
					this.caseData()
					this.status = false
				}
			},
			async caseStageList() {
				let res = await this.$u.api.lawCase.caseStageList(this.id)
				if (res) {
					let list = [{
						id: '',
						name: '全部记录'
					}]
					this.list = [...list, ...res.page.list]
				}
			},
			goPage(url) {
				console.log(url)
				this.show = false
				uni.navigateTo({
					url
				})
			},
			change(index) {
				this.current = index;
			},
			sub() {
				this.show = false
			},
			radioGroupChange(e) {
				console.log(e)
			},
			async deleteLitigant(id) {
				// let res = await this.$u.api.lawCase.deleteLitigant(id)
				// if(res){
				// }
			},
		}
	}
</script>
<style scoped lang="scss">
	.person_box {
		margin: 30rpx;
		display: flex;
	
	.add {
			text-align: center;
			margin: 0 auto;
			width: 70rpx;
			line-height: 35rpx;
			padding: 10rpx 16rpx;
			border: 1px dashed #ccc;
			background-color: #FFFFFF;
		}

		.p_list {
			overflow-x: auto;
			display: flex;
			padding: 10rpx;

			.p_item {
				box-shadow: 10rpx 10rpx 5rpx 0 rgba(190, 190, 190, 0.75);
				background-color: #FFFFFF;
				border-radius: 10rpx;
				width: 200rpx;
				height: 100%;
				flex-shrink: 0;
				margin-right: 35rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;
				font-weight: bold;
				font-size: 35rpx;

				.tag {
					position: absolute;
					top: 0;
					left: 0;
					font-size: 23rpx;
					text-align: center;
					width: 100rpx;
					padding: 5rpx;
					border-top-right-radius: 10rpx;
					border-bottom-left-radius: 10rpx;
					color: $u-type-primary-dark;
					background: $u-type-primary-light;
				}
			}
		}
	}

	.model_box {
		height: 800rpx;
		display: flex;
		flex-direction: column;

		.text {
			color: $u-type-primary;
		}

		.bottom {
			height: 100rpx;
			background-color: #EEF1F9;
			color: #A0A6BA;
		}
	}
</style>
