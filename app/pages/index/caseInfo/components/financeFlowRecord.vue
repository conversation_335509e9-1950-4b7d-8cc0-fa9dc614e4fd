<template>
	<view class="content">
		<u-sticky offset-top="80">
			<view style="background-color: #fff;border: 1px solid #f5f5f5;">
				<u-cell-item icon="plus" title="新增流水" @click='goPage'></u-cell-item>
			</view>
		</u-sticky>
		<u-card v-for="(item,index) in result.list" :key='item.id' margin="30rpx" :title="item.type==1?'收入':'支出'"
			:sub-title="item.happenDate">
			<view class="" slot="body">
				<view class="u-m-b-10">费用名称：{{item.name}}</view>
				<view class="u-m-b-10">发生人：{{item.happenUser.name}}</view>
				<view class="u-m-b-10">金额：{{item.money}} 元</view>
				<view class="u-m-b-10">关联：{{item.lawCase.name}}</view>
				<view>
					附件：
					<view class="u-flex" v-for="(i,k) in item.fileList" :key='k' @click="open(i.fullPath,i.fileType)">
						<image v-if="imgType.includes(i.fileType)"
							style="width: 80rpx;height: 80rpx;padding-right: 15rpx;" :src="i.fullPath"
							mode=""></image>
						<image v-if="docType.includes(i.fileType)"
							style="width: 80rpx;height: 80rpx;padding-right: 15rpx;" src="../../../../static/img/wd.png"
							mode=""></image>
							{{i.name}}
					</view>
				</view>
			</view>
		</u-card>
	</view>
</template>

<script>
	export default {
		props: {
			result: {
				type: Object | Array,
			},
			id: {
				type: String
			}
		},
		data() {
			return {
				show: false,
				imgType: ['png', 'jpg', 'jpeg'],
				docType: ['docx', 'doc'],
			}
		},
		created() {},
		methods: {
			open(path, type) {
				console.log(path, type)
				if (this.imgType.includes(type)) {
					var urls = [path]
					uni.previewImage({
						current: 0,
						urls //预览图片的地址，必须要数组形式，如果不是数组形式就转换成数组形式就可以
					})
				} else {
					uni.downloadFile({
						url: path,
						success: (res) => {
							if (res.statusCode === 200) {
								uni.openDocument({
									filePath: res.tempFilePath,
									// 如果文件名包含中文，建议使用escape(res.tempFilePath)转码，防止ios和安卓客户端导致的差异
									success: function(res) {
										console.log('打开文档成功');
									}
								});
							}
						}
					})
				}
			},
			goPage() {
				const pages = getCurrentPages(); //获取应用页面栈
				const {
					id,
					name
				} = pages[pages.length - 1].options //获取页面传递的信息
				uni.navigateTo({
					url: `/pages/index/caseInfo/finance/finance?id=${id}&name=${name}`
				})
			},
		}
	}
</script>
<style scoped lang="scss">

</style>
