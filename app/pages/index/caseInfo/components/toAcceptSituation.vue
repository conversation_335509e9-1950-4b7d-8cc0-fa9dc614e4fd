<template>
	<view class="content">
		<view class="u-flex-1" style="overflow-y: auto;"  v-if="result.lawCase">
			<!-- 基本信息 -->
			<view class="">
				<u-card 
				margin="30rpx"
				@click="goPage(`./result/result?id=${id}`)"
				:title="vuex_dict.case_type.find(dict=>dict.value==result.lawCase.type)['label']"
				>
				<view class="" slot="body">
					<view class="u-m-b-20">案由：{{result.lawCase.caseCause.name}}</view>
					<view>审理结果：{{result.lawCase.trialResult&&vuex_dict.trial_result.find(dict=>dict.value==result.lawCase.trialResult)['label']}}</view>
				</view>
				<view slot='foot'>
					裁决日期：{{result.lawCase.rulingDate}}
				</view>
				</u-card>
				<view class="person_box">
					<view class="add u-m-r-20" @click="goPage(`./addPerson/addPerson?id=${id}`)">
						添加承办人
					</view>
					<view class="u-flex-1 p_list">
							<view @click="goPage(`./personInfo/personInfo?id=${item.id}&gid=${item.lawCase.id}`)" class="p_item u-line-1" v-for="(item,key) in personList" :key='key'>
								<view>
									{{item.name}}
								</view>
								<view>
									职务：{{item.post}}
								</view>
								<view>
									科室：{{item.department}}
								</view>
							</view>
					</view>
				</view>
			</view>
		</view>
		
	</view>
</template>

<script>
	export default {
		props:{
			result:{
				type:Object|Array,
			},
			id:{
				type:String
			}
		},
		data(){
			return{
				personList:[]
			}
		},
		created() {
			this.caseUndertakePerson()
		},
		methods: {
			goPage(url){
				uni.navigateTo({
					url
				})
			},
			change(index) {
				this.current = index;
			},
			async caseUndertakePerson(){
				let res = await this.$u.api.lawCase.caseUndertakePerson(this.id)
				if(res){
					this.personList = res.page.list
				}
			},
		}
	}
</script>
<style scoped lang="scss">
.person_box{
	margin: 30rpx;
	display: flex;
	.add{
		text-align: center;
		margin:0 auto;width:70rpx;
		line-height:35rpx;
		padding: 10rpx 16rpx;
		border: 1px dashed #ccc;
		background-color: #FFFFFF;
	}
	.p_list{
		overflow-x: auto;
		display: flex;
		padding: 10rpx;
		.p_item{
			box-shadow: 10rpx 10rpx 5rpx 0 rgba(190,190,190,0.75);
			background-color: #FFFFFF;
			border-radius: 10rpx;
			width: 260rpx;
			height: 100%;
			flex-shrink: 0;
			margin-right: 35rpx;
			display: flex;
			flex-direction: column;
			justify-content: space-around;
			position: relative;
			font-size: 30rpx;
			padding: 0 15rpx;
			.tag{
				position: absolute;
				top: 0;
				left: 0;
				font-size: 23rpx;
				text-align: center;
				width: 	100rpx;
				padding: 5rpx;
				border-top-right-radius: 10rpx;
				border-bottom-left-radius: 10rpx;
				color: $u-type-primary-dark;
				background: $u-type-primary-light;
			}
		}
	}
}
</style>
