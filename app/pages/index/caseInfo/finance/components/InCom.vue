<template>
	<view style="background-color: #fff;">
		<view>
			<view>
				<u-picker v-model="showTime" mode="time" :params="params" @confirm='confirm'></u-picker>
				<view class="tit">
					新增收款
				</view>
				<u-form :model="form" ref="uForm" label-width='200' style="padding-left: 30rpx;">
					<u-form-item label="款项名称">
						<u-input v-model="form.name"/>
					</u-form-item>
					<u-form-item label="应收金额">
						<u-input type="number" v-model="form.receivableMoney"/>
					</u-form-item>
				</u-form>
				<view class="tit" style="display: flex;justify-content: space-between;align-items: center;">
					<text>收款记录</text>
					<u-button size="mini" style="margin: 0;" @click="add">添加记录</u-button>
				</view>
				<view style="border-bottom: 30rpx solid #f5f5f5;" v-for="(item,key) in flowRecordList" :key='key'>
					<u-form  label-width='200' style="padding-left: 30rpx;">
						<u-form-item label="实收金额">
							<u-input v-model="item.money" type="number"/>
						</u-form-item>
						<u-form-item label="收款日期">
							<u-input disabled v-model="item.happenDate" @click='checkTime(key)'/>
						</u-form-item>
					</u-form>
					<u-cell-item title="上传附件:" hover-class='none' @click='upload(key)' :arrow="false">
						<view slot="right-icon">
							<u-icon name="plus" size="35"></u-icon>
						</view>
					</u-cell-item>
					<view v-for="(item,key) in item.fileJsonStr" :key='key'>
						<u-cell-item :title="item.name" hover-class='none' :arrow="false" :border-bottom='true'
							@click="open(item.fullPath,item.fileType)">
							<view slot="icon">
								<image v-if="imgType.includes(item.fileType)"
									style="width: 80rpx;height: 80rpx;padding-right: 15rpx;" :src="item.fullPath" mode="">
								</image>
								<image v-if="docType.includes(item.fileType)"
									style="width: 80rpx;height: 80rpx;padding-right: 15rpx;"
									src="../../../../../static/img/wd.png" mode=""></image>
							</view>
							<view slot="right-icon" @click.stop="delFile(key)">
								<u-icon name="close" color="red" size="35"></u-icon>
							</view>
						</u-cell-item>
					</view>
					<view @click="del(key)" class="u-padding-top-30 u-p-r-30" style="background-color: #f5f5f5;text-align: right;color: red;">删除记录</view>
				</view>
				
			</view>
		</view>
		<view class="u-padding-25" style="background-color: #f5f5f5;">
			<u-button type="primary" @click="submit">提交</u-button>
		</view>
	</view>
</template>

<script>
	import httpUrl from '@/utils/config.js'
	export default {
		components: {},
		data() {
			return {
				id: '',
				gid: '',
				cur:0,
				params: {
					year: true,
					month: true,
					day: true,
					hour: true,
					minute: true,
					second: true
				},
				showTime:false,
				form: {
					type: 1,
					receivableMoney: '',
					name: ''
				},
				flowRecordList:[
					{
						money:'',
						happenDate:'',
						fileJsonStr:[],
						fileList:[],
					},
				],
				fileList: [],
				imgType: ['png', 'jpg', 'jpeg'],
				docType: ['docx', 'doc'],
			};
		},
		onLoad(e) {},
		onShow() {

		},
		methods: {
			confirm(e){
				let {year,month,day,hour,minute,second} = e
				this.flowRecordList[this.cur].happenDate = `${year}-${month}-${day}  ${hour}:${minute}:${second}`
				console.log(e)
			},
			checkTime(key){
				this.cur = key
				this.showTime = true
			},
			add(){
				// 解决Vue视图不更新的情况(重新赋值法)
				var arr = JSON.parse(JSON.stringify(this.flowRecordList));
				arr.push({
					money:'',
					happenDate:'',
					fileJsonStr:[],
					fileList:[],
				})
				this.flowRecordList = JSON.parse(JSON.stringify(arr));

				console.log(this.flowRecordList,arr)
			},
			getFileType(name) {
				var fileName = name.lastIndexOf("."); //取到文件名开始到最后一个点的长度

				var fileNameLength = name.length; //取到文件名长度

				var fileFormat = name.substring(fileName + 1, fileNameLength);
				console.log(fileFormat)
				return fileFormat
			},
			delFile(key) {
				this.fileList.splice(key, 1)
			},
			del(key) {
				if(this.flowRecordList.length==1) return
				var arr = JSON.parse(JSON.stringify(this.flowRecordList));
				arr.splice(key, 1)
				this.flowRecordList = JSON.parse(JSON.stringify(arr));
			},
			upload(key) {
				uni.chooseImage({
					count: 1, // 最多可以选择的图片张数，默认9
					sizeType: ['compressed'], // original 原图，compressed 压缩图，默认二者都有
					sourceType: ['album', 'camera'], // album 从相册选图，camera 使用相机，默认二者都有
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						uni.uploadFile({
							url: httpUrl + '/law/sys/file/webupload/upload', //仅为示例，非真实的接口地址
							filePath: tempFilePaths[0],
							name: 'file',
							header: {
								token: this.vuex_token
							},
							formData: {
								'uploadPath': '/lawcase/todoFile/',
							},
							success: (uploadFileRes) => {
								this.$u.toast('上传成功')
								let res = JSON.parse(uploadFileRes.data)
								this.flowRecordList[key].fileJsonStr.push({
									name: res.name,
									path: res.url,
									fileType: this.getFileType(res.name),
									fullPath: `http://oa.lgy0999.com/law` + res.url
								})
								this.flowRecordList[key].fileList.push({
									name: res.name,
									path: res.url,
									fileType: this.getFileType(res.name),
									fullPath: `http://oa.lgy0999.com/law` + res.url
								})
								console.log(res);
							}
						});
					}
				})
			},
			open(path, type) {
				console.log(path, type)
				if (this.imgType.includes(type)) {
					var urls = [path]
					uni.previewImage({
						current: 0,
						urls //预览图片的地址，必须要数组形式，如果不是数组形式就转换成数组形式就可以
					})
				} else {
					uni.downloadFile({
						url: path,
						success: (res) => {
							if (res.statusCode === 200) {
								uni.openDocument({
									filePath: res.tempFilePath,
									// 如果文件名包含中文，建议使用escape(res.tempFilePath)转码，防止ios和安卓客户端导致的差异
									success: function(res) {
										console.log('打开文档成功');
									}
								});
							}
						}
					})
				}
			},
			async submit(data) {
				const pages= getCurrentPages();//获取应用页面栈
				const {id,name} = pages[pages.length - 1].options//获取页面传递的信息
				let title = ''
				if(this.form.name==''){
					this.$u.toast('款项名称不能为空')
					return
				}
				if(this.form.receivableMoney==''){
					this.$u.toast('应收金额不能为空')
					return
				}
				let flag = this.flowRecordList.some(item=>item.money==''||item.happenDate=='')
				if(flag){
					this.$u.toast('请完善收款记录')
					return
				}
				
				let obj = {
					flowRecordList:this.flowRecordList,
					lawCase:{
						id,
						name
					},
					...this.form
				}
				let res = await this.$u.api.lawCase.flowRecordsaveInfo(obj)
				if(res){
					this.$u.toast('提交成功')
					uni.navigateBack()
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.tit {
		padding: 30rpx;
		background-color: #f5f5f5;
	}
</style>
