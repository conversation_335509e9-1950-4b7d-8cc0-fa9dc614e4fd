<template>
	<view>
		<view style="background-color: #fff;">
			<view>
				<view class="tit">
					新增支出
				</view>
				<u-picker v-model="showTime" mode="time" :params="params" @confirm='confirm'></u-picker>
				<u-form :error-type="errorType" :model="form" ref="uForm1" label-width='200' style="padding-left: 15px;">
					<u-form-item label="款项名称" prop="name">
						<u-input v-model="form.name"/>
					</u-form-item>
					<u-form-item label="金额" prop="money">
						<u-input  type="number" v-model="form.money"/>
					</u-form-item>
					<u-form-item label="发生日期" prop="happenDate">
						<u-input disabled v-model="form.happenDate" @click='showTime=true'/>
					</u-form-item>
				</u-form>
				<u-cell-item title="上传附件:" hover-class='none' @click='upload' :arrow="false">
					<view slot="right-icon">
						<u-icon name="plus" size="35"></u-icon>
					</view>
				</u-cell-item>
			</view>
			<view v-for="(item,key) in fileList" :key='key'>
				<u-cell-item :title="item.name" hover-class='none' :arrow="false" :border-bottom='true'
					@click="open(item.fullPath,item.fileType)">
					<view slot="icon">
						<image v-if="imgType.includes(item.fileType)"
							style="width: 80rpx;height: 80rpx;padding-right: 15rpx;" :src="item.fullPath" mode="">
						</image>
						<image v-if="docType.includes(item.fileType)"
							style="width: 80rpx;height: 80rpx;padding-right: 15rpx;"
							src="../../../../../static/img/wd.png" mode=""></image>
					</view>
					<view slot="right-icon" @click.stop="delFile(key)">
						<u-icon name="close" color="red" size="35"></u-icon>
					</view>
				</u-cell-item>
			</view>
		</view>
		<view class="u-padding-25" style="background-color: #f5f5f5;">
			<u-button type="primary" @click="submit">提交</u-button>
		</view>
	</view>
</template>

<script>
	import httpUrl from '@/utils/config.js'
	export default {
		components: {},
		data() {
			return {
				errorType: ['toast'],
				id: '',
				gid: '',
				showTime:false,
				form: {
					name: '',
					type: 2,
					money: '',
					happenDate: ''
				},
				params: {
					year: true,
					month: true,
					day: true,
					hour: true,
					minute: true,
					second: true
				},
				rules: {
					name: [{
						required: true,
						message: '款项名称',
						// 可以单个或者同时写两个触发验证方式
						trigger: 'blur'
					}],
					money: [{
						required: true,
						message: '请输入金额',
						trigger: 'blur'
					}],
					happenDate: [{
						required: true,
						message: '请选择日期',
						// 可以单个或者同时写两个触发验证方式
						trigger: 'blur'
					}],
				},
				fileList: [],
				imgType: ['png', 'jpg', 'jpeg'],
				docType: ['docx', 'doc'],
			};
		},
		onLoad(e) {},
		mounted() {
			console.log(this.$refs,101010)
			this.$refs.uForm1.setRules(this.rules);
		},
		onShow() {
			
		},
		methods: {
			confirm(e){
				let {year,month,day,hour,minute,second} = e
				this.form.happenDate = `${year}-${month}-${day}  ${hour}:${minute}:${second}`
			},
			getFileType(name) {
				var fileName = name.lastIndexOf("."); //取到文件名开始到最后一个点的长度

				var fileNameLength = name.length; //取到文件名长度

				var fileFormat = name.substring(fileName + 1, fileNameLength);
				console.log(fileFormat)
				return fileFormat
			},
			delFile(key) {
				this.fileList.splice(key, 1)
			},
			upload() {
				uni.chooseImage({
					count: 1, // 最多可以选择的图片张数，默认9
					sizeType: ['compressed'], // original 原图，compressed 压缩图，默认二者都有
					sourceType: ['album', 'camera'], // album 从相册选图，camera 使用相机，默认二者都有
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						uni.uploadFile({
							url: httpUrl + '/law/sys/file/webupload/upload', //仅为示例，非真实的接口地址
							filePath: tempFilePaths[0],
							name: 'file',
							header: {
								token: this.vuex_token
							},
							formData: {
								'uploadPath': '/lawcase/todoFile/',
							},
							success: (uploadFileRes) => {
								this.$u.toast('上传成功')
								let res = JSON.parse(uploadFileRes.data)
								this.fileList.push({
									name: res.name,
									path: res.url,
									fileType: this.getFileType(res.name),
									fullPath: `http://oa.lgy0999.com/law` + res.url
								})
								console.log(res);
							}
						});
					}
				})
			},
			open(path, type) {
				console.log(path, type)
				if (this.imgType.includes(type)) {
					var urls = [path]
					uni.previewImage({
						current: 0,
						urls //预览图片的地址，必须要数组形式，如果不是数组形式就转换成数组形式就可以
					})
				} else {
					uni.downloadFile({
						url: path,
						success: (res) => {
							if (res.statusCode === 200) {
								uni.openDocument({
									filePath: res.tempFilePath,
									// 如果文件名包含中文，建议使用escape(res.tempFilePath)转码，防止ios和安卓客户端导致的差异
									success: function(res) {
										console.log('打开文档成功');
									}
								});
							}
						}
					})
				}
			},
			 submit() {
				const pages= getCurrentPages();//获取应用页面栈
				const {id,name} = pages[pages.length - 1].options//获取页面传递的信息
				this.$refs.uForm1.validate(async valid => {
					if (valid) {
						let obj = {
							fileJsonStr: JSON.stringify(this.fileList),
							'lawCase.id':id,
							'lawCase.name':name,
							...this.form
						}
						console.log(obj,'23232')
						let res = await this.$u.api.lawCase.financeFlowRecordSave(obj)
						if (res) {
							this.$u.toast('提交成功')
							uni.navigateBack()
						}
					} else {
						console.log('验证失败');
					}
				});

			}
		},
	}
</script>

<style scoped lang="scss">
	.tit {
		padding: 30rpx;
		background-color: #f5f5f5;
	}
</style>
