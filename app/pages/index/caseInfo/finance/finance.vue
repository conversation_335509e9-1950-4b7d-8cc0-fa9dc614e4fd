<template>
	<view style="background-color: #fff;">
		<u-subsection :list="list" :current="current" @change="change"></u-subsection>
		<InCom v-if="current==0"/>
		<OutCom v-if="current==1"/>
	</view>
</template>

<script>
	import InCom from './components/InCom.vue'
	import OutCom from './components/OutCom.vue'
	export default {
		components: {
			InCom,
			OutCom,
		},
		data() {
					return {
						list: [
							{
								name: '新增收款'
							}, 
							{
								name: '新增支出'
							}, 
						],
						current: 0
					}
				},
		methods: {
			change(index) {
				this.current = index;
			}
		}
	}
</script>

<style scoped lang="scss">

</style>
