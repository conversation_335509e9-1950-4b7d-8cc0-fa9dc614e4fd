const formConfig = function(vm) {
	return new Promise(async (resolve, rejict) => {
		let result = [{
				title: '基础信息',
				key: 'lawCase',
				type: 'Object',
				form: [
					{
						label: '案件名称',
						key: 'name',
						name: 'name',
						value: '',
						type: 'input',
						config: {},
						rules: [{
							required: true,
							message: '请输入案件名称',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '案件类型',
						key: 'type',
						name: 'type',
						value: '',
						type: 'select',
						config: {
							mode: 'single-column',
							label: 'label',
							value: 'value'
						},
						list: vm.vuex_dict.case_type,
						rules: [{
							required: true,
							message: '请选择案件类型',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '案件程序',
						key: 'caseProgram',
						name: 'caseProgram',
						value: '',
						type: 'select',
						config: {
							mode: 'single-column',
							label: 'label',
							value: 'value'
						},
						list: [],
						rules: [{
							required: true,
							message: '请选择案件程序',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '案由',
						key: 'caseCause',
						name: 'caseCause',
						value: '',
						type: 'select',
						config: {
							mode: 'mutil-column-auto',
							label: 'name',
							value: 'id'
						},
						list: [],
						rules: [{
							required: true,
							message: '请选择案由',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '委托时间',
						key: 'entrustDate',
						name: 'entrustDate',
						value: '',
						type: 'picker',
						config: {
							mode: 'time',
							params: {
								year: true,
								month: true,
								day: true,
							},
						},
						list: [],
						rules: [{
							required: true,
							message: '请选择委托时间',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '案件标的',
						key: 'subjectMatter',
						name: 'subjectMatter',
						value: '',
						type: 'input',
						config: {
							rightText: '元',
							type: 'digit'
						},
						rules: [{
							required: true,
							message: '请输入案件标的',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '合同金额',
						key: 'contractMoney',
						name: 'contractMoney',
						value: '',
						type: 'input',
						config: {
							rightText: '元',
							type: 'digit'
						},
						rules: [{
							required: true,
							message: '请输入合同金额',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '收费方式',
						key: 'chargeMode',
						name: 'chargeMode',
						value: '',
						type: 'select',
						config: {
							mode: 'single-column',
							label: 'label',
							value: 'value'
						},
						list: vm.vuex_dict.charge_mode,
						rules: [{
							required: true,
							message: '请选择收费方式',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '胜诉金额',
						key: 'winMoney',
						name: 'winMoney',
						value: '',
						type: 'input',
						config: {
							rightText: '元',
							type: 'digit'
						},
						rules: [{
							required: true,
							message: '请输入胜诉金额',
							trigger: ['change', 'blur'],
						}]
					},
					
					// {
					// 	label: '收费备注',
					// 	key: 'chargeRemarks',
					// 	name: 'chargeRemarks',
					// 	value: '',
					// 	type: 'input',
					// 	config: {
					// 		type:'textarea'
					// 	},
					// 	rules: [{
					// 		required: false,
					// 		message: '请输入收费备注',
					// 		trigger: ['change', 'blur'],
					// 	}]
					// },
				]
			},
			{
					title: '受理信息',
					key: 'lawCase',
					type: 'Object',
					form: [
						{
							label: '地区',
							key: 'acceptUnitArea',
							name: 'acceptUnitArea',
							value: '',
							type: 'input',
							config: {},
							rules: [{
								required: true,
								message: '请输入地区',
								trigger: ['change', 'blur'],
							}]
						},
						{
							label: '单位类型',
							key: 'acceptUnitType',
							name: 'acceptUnitType',
							value: '',
							type: 'select',
							config: {
								mode: 'single-column',
								label: 'label',
								value: 'value'
							},
							list: vm.vuex_dict.accept_unit_type,
							rules: [{
								required: true,
								message: '请选择单位类型',
								trigger: ['change', 'blur'],
							}]
						},
						{
							label: '受理单位',
							key: 'acceptUnitName',
							name: 'acceptUnitName',
							value: '',
							type: 'input',
							config: {},
							rules: [{
								required: true,
								message: '请输入受理单位',
								trigger: ['change', 'blur'],
							}]
						},
						{
							label: '立案日期',
							key: 'recordDate',
							name: 'recordDate',
							value: '',
							type: 'picker',
							config: {
								mode: 'time',
								params: {
									year: true,
									month: true,
									day: true,
								},
							},
							list: [],
							rules: [{
								required: true,
								message: '请选择立案日期',
								trigger: ['change', 'blur'],
							}]
						},
						{
							label: '裁决日期',
							key: 'rulingDate',
							name: 'rulingDate',
							value: '',
							type: 'picker',
							config: {
								mode: 'time',
								params: {
									year: true,
									month: true,
									day: true,
								},
							},
							list: [],
							rules: [{
								required: true,
								message: '请选择裁决日期',
								trigger: ['change', 'blur'],
							}]
						},
						{
							label: '审理结果',
							key: 'trialResult',
							name: 'trialResult',
							value: '',
							type: 'select',
							config: {
								mode: 'single-column',
								label: 'label',
								value: 'value'
							},
							list: vm.vuex_dict.trial_result,
							rules: [{
								required: true,
								message: '请选择审理结果',
								trigger: ['change', 'blur'],
							}]
						},
					]
				},
				{
						title: '结案归档',
						key: 'lawCase',
						type: 'Object',
						form: [
							{
								label: '结案日期',
								key: 'settleCaseDate',
								name: 'settleCaseDate',
								value: '',
								type: 'picker',
								config: {
									mode: 'time',
									params: {
										year: true,
										month: true,
										day: true,
									},
								},
								list: [],
								rules: [{
									required: true,
									message: '请选择结案日期',
									trigger: ['change', 'blur'],
								}]
							},
							{
								label: '结案状态',
								key: 'settleCaseStatus',
								name: 'settleCaseStatus',
								value: '',
								type: 'select',
								config: {
									mode: 'single-column',
									label: 'label',
									value: 'value'
								},
								list: vm.vuex_dict.settle_case_status,
								rules: [{
									required: true,
									message: '请选择结案状态',
									trigger: ['change', 'blur'],
								}]
							},
							{
								label: '实际回款',
								key: 'actualBackMoney',
								name: 'actualBackMoney',
								value: '',
								type: 'input',
								config: {
									rightText: '元',
									type: 'digit'
								},
								rules: [{
									required: true,
									message: '请输入实际回款',
									trigger: ['change', 'blur'],
								}]
							},
						]
					},
		]
		vm.$data.formConfig = result
		resolve(result)
	})
}

export default formConfig
