<template>
	<view style="height: 100%;background-color: #FFFFFF;padding: 30rpx;">
		<view class="u-flex u-row-between" style="color: #909399;">
			<view class="u-font-30">办案策略</view>
			<view>
				<u-button shape="circle" size="mini" @click="goPage()">新增策略</u-button>
				<!-- <u-icon name="plus" class="u-margin-right-5"></u-icon>
				新增 -->
			</view>
		</view>
		<view v-if="list.length>0">
				<u-collapse :item-style="itemStyle" :arrow="arrow" :accordion="true" >
					<u-collapse-item :index="index" :title="`策略${index+1}`" v-for="(item, index) in list" :key="index">
						<u-cell-item @click="goPage(item.id)" icon="question" :arrow="false" :title="`问题描述：${item.question}`"></u-cell-item>
						<u-cell-item @click="goPage(item.id)" icon="file-text" :arrow="false" :title="`处理结果：${item.result}`"></u-cell-item>
						<u-cell-item @click="goPage(item.id)" icon="checkmark-circle" :arrow="false" :title="`解决方案：${item.solution}`"></u-cell-item>
					</u-collapse-item>
				</u-collapse>
		</view>
		<block v-else>
			<u-empty text="暂无数据" mode="list"></u-empty>
		</block>
	</view>
</template>

<script>
	export default{
		props:{
			result:{
				type:Object|Array,
			},
			id:{
				type:String
			}
		},
		computed:{
			list:{
				get(){
					return this.result?.list?.map(item=>{
						item['show'] = false
						return item
					})
				}
			}
		},
		data() {
			return {
				accordion: true,
				arrow: true,
				itemStyle: {
							border: '1px solid rgb(230, 230, 230)',
							marginTop: '20px',
							padding: '0 8rpx'
						},
			}
		},
		methods:{
			goPage(id){
				let url;
				if(id){
					url = `/pages/index/caseInfo/info/addCaseHandleStrategy/addCaseHandleStrategy?id=${this.id}&cid=${id}`
				}else{
					url = `/pages/index/caseInfo/info/addCaseHandleStrategy/addCaseHandleStrategy?id=${this.id}`
				}
				uni.navigateTo({
					url
				})
			},
		}
	}
</script>

<style scoped lang="scss">
/deep/ .u-cell{
	padding-left: 0;
}
</style>
