<template>
	<view style="height: 100%;background-color: #FFFFFF;padding: 30rpx;" v-if="list">
		<view class="u-flex u-row-between" style="color: #909399;">
			<view class="u-font-30">关联案件</view>
			<view> 
				<u-button shape="circle" size="mini" @click="show=true">新增关联</u-button>
			</view>
		</view>
		<block v-if="list.length>0">
			<u-card :title="item.relationCase.caseProgram.name"  v-for="(item,key) in list" :key='key'>
				<view class="" slot="body">
					{{item.relationCase.name}}
				</view>
				<view class="u-flex u-row-right" slot="foot" >
					<u-icon @click="del(item.id)" name="trash-fill" size="34" color="red" label-color='red' label="解除关联"></u-icon>
				</view>
			</u-card>
		</block>
		<block v-else>
			<u-empty text="暂无数据" mode="list"></u-empty>
		</block>
		<u-modal ref='m' v-model="show" :show-title='true' show-cancel-button @confirm='addCaseRelation'>
					<view class="slot-content">
						<view class="u-padding-30">
						<u-input  type="select" v-model="value" :select-open="showList" @click='showList=true'  placeholder="请选择关联的案件"/>
							
						</view>
					</view>
				</u-modal>
				<u-select 
				v-model="showList" 
				:list="infoList"
				label-name='name'
				value-name='id'
				@confirm='confirm'
				></u-select>
	</view>
</template>

<script>
	export default{
		props:{
			result:{
				type:Object|Array,
			},
			id:{
				type:String
			}
		},
		data(){
			return{
				show:false,
				showList:false,
				infoList:[],
				// list:[],
				value:'',
				rid:''
			}
		},
		computed:{
			list(){
				return this.result.list
			}
		},
		watch:{
			show(n){
				if(n) this.caseRelationInfoList()
			}
		},
		methods:{
			async caseRelationInfoList(){
				this.value = ''
				this.rid = ''
				let res = await this.$u.api.lawCase.caseRelationInfoList(this.id)
				if(res){
					this.infoList = res.data
				}
			},
			confirm(e){
				if(!e) return
				this.value = e[0]['label']
				this.rid = e[0]['value']
			},
			async del(id){
				let res = await this.$u.api.lawCase.deleteCaseRelation(id)
				if(res){
					this.$emit('getList')
				}
			},
			async addCaseRelation(){
				if(!this.rid){
					this.$u.toast('请选择关联案件')
					this.show = true
					return
				}
				let res = await this.$u.api.lawCase.addCaseRelation({
					'lawCase.id':this.id,
					'relationCase.id':this.rid,
				})
				if(res){
					this.$emit('getList')
					this.show = false
				}
			}
		},
		
	}
</script>

<style>
</style>
