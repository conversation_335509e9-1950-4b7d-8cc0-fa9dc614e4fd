<template>
	<view class="content">
		<view class="u-flex-1" style="overflow-y: auto;">
			<u-modal @confirm='confirm' v-model="show" content="是否删除案件？" show-cancel-button></u-modal>
			<u-cell-group v-if="info" title="基本信息">
				<u-cell-item title="案件名称" :arrow="false" :value="info.name"></u-cell-item>
				<u-cell-item title="案件类型" :arrow="false" :value="info.type&&vuex_dict.case_type.find(dict=>dict.value==info.type)['label']"></u-cell-item>
				<u-cell-item title="案件程序" :arrow="false" :value="info.caseProgram.name"></u-cell-item>
				<u-cell-item title="案由" :arrow="false" :value="info.caseCause.name"></u-cell-item>
				<u-cell-item title="委托时间" :arrow="false" :value="info.entrustDate"></u-cell-item>
				<u-cell-item title="案件标的" :arrow="false" :value="info.subjectMatter"></u-cell-item>
				<u-cell-item title="合同金额" :arrow="false" :value="info.contractMoney"></u-cell-item>
				<u-cell-item title="收费方式" :arrow="false" :value="info.chargeMode&&vuex_dict.charge_mode.find(dict=>dict.value==info.chargeMode)['label']"></u-cell-item>
				<u-cell-item title="胜诉金额" :arrow="false" :value="info.winMoney"></u-cell-item>
			</u-cell-group>
			<u-cell-group v-if="info" title="受理信息">
				<u-cell-item title="地区" :arrow="false" :value="info.acceptUnitArea"></u-cell-item>
				<u-cell-item title="单位类型" :arrow="false" :value="info.acceptUnitType&&vuex_dict.accept_unit_type.find(dict=>dict.value==info.acceptUnitType)['label']"></u-cell-item>
				<u-cell-item title="受理单位" :arrow="false" :value="info.acceptUnitName"></u-cell-item>
				<u-cell-item title="立案日期" :arrow="false" :value="info.recordDate"></u-cell-item>
				<u-cell-item title="裁决日期" :arrow="false" :value="info.rulingDate"></u-cell-item>
				<u-cell-item title="审理结果" :arrow="false" :value="info.trialResult&&vuex_dict.trial_result.find(dict=>dict.value==info.trialResult)['label']"></u-cell-item>
			</u-cell-group>
			<u-cell-group v-if="info" title="结案归档">
				<u-cell-item title="结案日期" :arrow="false" :value="info.settleCaseDate"></u-cell-item>
				<u-cell-item title="结案状态" :arrow="false" :value="info.settleCaseStatus&&vuex_dict.settle_case_status.find(dict=>dict.value==info.settleCaseStatus)['label']"></u-cell-item>
				<u-cell-item title="实际回款" :arrow="false" :value="info.actualBackMoney"></u-cell-item>
			</u-cell-group>
			<u-cell-group v-if="info" title="办案人员">
				<u-cell-item title="主办人员" :arrow="false" :value="info.hostUser.name"></u-cell-item>
			</u-cell-group>
		</view>
		<view class="u-padding-30">
			<u-button type="primary" @click="goPage">编辑</u-button>
			<u-button class="u-margin-top-30" type="error" @click="show=true">删除</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			result:{
				type:Object|Array,
			},
			id:{
				type:String,
			}
		},
		data() {
			return {
				// info:'',
				show:false
			};
		},
		computed:{
			info:{
				get(){
					return this.result.lawCase
				}
			}
		},
		created() {
			// this.detailInfo()
		},
		methods:{
			goPage(){
				uni.navigateTo({
					url:`/pages/index/caseInfo/info/editCase/editCase?id=${this.id}`
				})
			},
			detailInfo(){
				console.log(this.result,'++++++++++++')
				// this.info = this.result.lawCase
			},
			async confirm(){
				let res = await this.$u.api.lawCase.delCase(this.id)
				if(res){
					this.$u.toast('删除成功')
					uni.navigateBack({
						delta:2
					})
				}
			}
		}
	}
</script>

<style lang="scss">

</style>
