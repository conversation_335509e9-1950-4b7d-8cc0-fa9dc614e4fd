<template>
	<view class="content">
		<u-tabs :list="list" :is-scroll="false" :current="current" @change="change"></u-tabs>
		<view class="u-flex-1 u-border-top" style="overflow: auto;" v-if="show">
			<component :result='result' :id="id" :is="list[current].list" @getList='getData'></component>
		</view>
	</view>
</template>

<script>
	import detailInfo from './components/detail.vue'
	import caseRelationList from './components/caseRelationList.vue'
	import caseHandleStrategyList from './components/caseHandleStrategyList.vue'
	export default {
		components:{
			detailInfo,
			caseRelationList,
			caseHandleStrategyList,
		},
		data() {
			return {
				show:true,
				current: 0,
				list: [{
					name: '案件详情',
					list:'detailInfo'
				}, {
					name: '关联案件',
					list:'caseRelationList'
				}, {
					name: '办案策略',
					list:'caseHandleStrategyList',
				}],
				id:'',
				result:{}
			};
		},
		onLoad(e) {
			this.id = e.id
		},
		computed:{
			mtype(){
				return this.list[this.current].list
			}
		},
		onShow() {
			this.getData()
		},
		methods:{
			change(index) {
				this.show = false
				this.current = index;
				this.getData()
			},
			async getData(){
				let res = await this.$u.api.lawCase[this.mtype](this.id)
				if(res){
					if(res.data){
						this.result = res.data
					}else{
						this.result = res.page
					}
					this.show = true
				}
			},
		}
	}
</script>

<style lang="scss" scoped>

</style>
