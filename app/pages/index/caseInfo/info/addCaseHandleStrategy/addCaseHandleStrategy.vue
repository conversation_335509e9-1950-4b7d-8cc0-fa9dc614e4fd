<template>
	<view>
		<creatForm ref='creatForm' :formConfig='formConfig'  @submit='submit' />
	</view>
</template>

<script>
	import creatForm from '@/components/creatForm.vue'
	import formConfig from './formConfig.js'
	export default {
		components: {
			creatForm
		},
		data() {
			return {
				formConfig: {},
				formObj: {},
				id:'',
				cid:'',
			};
		},
		onLoad(e) {
			formConfig(this)
			this.id = e.id
			this.cid = e.cid
			
		},
		onShow() {
				this.getCaseHandleStrategy()
		},
		methods:{
			async getCaseHandleStrategy(){
				let result = await this.$u.api.lawCase.getCaseHandleStrategy(this.cid)
				if(result){
					let res = result.caseHandleStrategy
					this.formConfig[0].form.map(item => {
						item['value'] = res[item.key]
						this.$refs['creatForm'].form[item.key] = res[item.key]
					})
					this.formConfig[0].key = +new Date()
				}
			},
			async addCaseHandleStrategy(res){
				let result = await this.$u.api.lawCase.addCaseHandleStrategy(res)
				if(result){
					uni.navigateBack()
					this.$u.toast('提交成功')
				}
			},
			submit(res){
				res['lawCase'] = {id:this.id}
				if(this.cid){
					res['id'] = this.cid
				}
				this.addCaseHandleStrategy(res)
				console.log(res,'tijiao')
			}
		}
	}
</script>

<style lang="scss">

</style>
