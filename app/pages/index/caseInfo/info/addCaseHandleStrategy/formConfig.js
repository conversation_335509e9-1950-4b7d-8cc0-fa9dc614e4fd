const formConfig = function(vm) {
	return new Promise(async (resolve, rejict) => {
		let result = [
			{
				title: '办案策略',
				key: 'concernPersonList',
				type: 'Array',
				form: [
					{
						label: '问题描述',
						key: 'question',
						name: 'question',
						value: '',
						type: 'input',
						config: {
							type:'textarea'
						},
						rules: [{
							required: false,
							message: '请输入问题描述',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '处理结果',
						key: 'result',
						name: 'result',
						value: '',
						type: 'input',
						config: {
							type:'textarea'
						},
						rules: [{
							required: false,
							message: '请输入处理结果',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '解决方案',
						key: 'solution',
						name: 'solution',
						value: '',
						type: 'input',
						config: {
							type:'textarea'
						},
						rules: [{
							required: false,
							message: '请输入解决方案',
							trigger: ['change', 'blur'],
						}]
					},
				]
			}
		]
		vm.$data.formConfig = result
		resolve(result)
	})
}

export default formConfig
