<template>
	<view>
		<creatForm ref='creatForm' :formConfig='formConfig' @confirm='confirm' @submit='submit' />
	</view>
</template>

<script>
	import creatForm from '@/components/creatForm.vue'
	import formConfig from './formConfig.js'
	export default {
		components: {
			creatForm
		},
		data() {
			return {
				id:'',
				gid:'',
				formConfig: {},
				formObj: {},
				info:{},
			};
		},
		onLoad(e) {
			this.id = e.id
			this.gid = e.gid
			formConfig(this)
		},
		onShow() {
			let title = this.id?'编辑执行情况':'新增执行情况'
			uni.setNavigationBarTitle({
				title
			})
			if(this.id){
				this.caseExecuteSituationInfo()
			}
		},
		methods:{
			async caseExecuteSituationInfo(){
				let res = await this.$u.api.lawCase.caseExecuteSituationInfo(this.id)
				if(res){
					Object.assign(this.info,{
						...res.caseExecuteSituation,
						status:this.vuex_dict.execute_status.find(dict=>dict.value==res.caseExecuteSituation.status)['label'],
						measures:this.vuex_dict.execute_measures.find(dict=>dict.value==res.caseExecuteSituation.measures)['label'],
					})
					this.confirm({
						name:'status',
						label: this.vuex_dict.execute_status.find(dict=>dict.value==res.caseExecuteSituation.status)['label'],
						value: this.vuex_dict.execute_status.find(dict=>dict.value==res.caseExecuteSituation.status)['value'],
					})
					this.confirm({
						name:'measures',
						label: this.vuex_dict.execute_measures.find(dict=>dict.value==res.caseExecuteSituation.measures)['label'],
						value: this.vuex_dict.execute_measures.find(dict=>dict.value==res.caseExecuteSituation.measures)['value'],
					})
					// this.confirm({
					// 	name:'executeStatus',
					// 	label: this.vuex_dict.execute_status.find(dict=>dict.value==res.casePropertyPreservation.executeStatus)['label'],
					// 	value: this.vuex_dict.execute_status.find(dict=>dict.value==res.casePropertyPreservation.executeStatus)['value'],
					// })
					this.formConfig[0].form.map(item=>{
						item['value'] = String(this.info[item.key])
						this.$refs['creatForm'].form[item.key] = String(this.info[item.key])
						return item
					})
				}
			},
			confirm(res) {
				this.formObj = {
					...this.formObj,
					[res['name']]: {
						id: res.value,
						name: res.label,
					}
				}
			},
			async submit(data){
				let res = this.$u.deepClone(data);
				let o = [
					'status',
					'measures',
				]
				Object.keys(this.formObj).forEach((key)=>{
					if(o.includes(key)){
						res[key] = this.formObj[key]['id']
					}
				})
				res['lawCase'] = {
					id:this.gid
				}
				res['id'] = this.id||''
				res['applyDate'] = res['applyDate']?res['applyDate'] + ' 00:00:00':''
				console.log(res)
				let result = await this.$u.api.lawCase.saveCaseExecuteSituation(res)
				if(result){
					this.$u.toast('提交成功')
					uni.navigateBack()
				}
			
				
			}
		}
	}
</script>

<style scoped lang="scss">

</style>
