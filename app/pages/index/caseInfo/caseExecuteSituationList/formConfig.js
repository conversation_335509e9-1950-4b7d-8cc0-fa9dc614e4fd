const formConfig = function(vm) {
	return new Promise(async (resolve, rejict) => {
		let result = [{
			title: '执行情况',
			key: 'concernPersonList',
			type: 'Array',
			add: false,
			form: [{
					label: '申请执行日',
					key: 'applyDate',
					name: 'applyDate',
					value: '',
					type: 'picker',
					config: {
						mode: 'time',
						params: {
							year: true,
							month: true,
							day: true,
						},
					},
					list: [],
					rules: [{
						required: true,
						message: '请选择日期',
						trigger: ['change', 'blur'],
					}]
				},{
					label: '受理单位',
					key: 'acceptUnit',
					name: 'acceptUnit',
					value: '',
					type: 'input',
					config: {},
					rules: [{
						required: true,
						message: '请输入受理单位',
						trigger: ['change', 'blur'],
					}]
				},
				{
					label: '受理案号',
					key: 'number',
					name: 'number',
					value: '',
					type: 'input',
					config: {},
					rules: [{
						required: true,
						message: '请输入受理案号',
						trigger: ['change', 'blur'],
					}]
				},
				{
					label: '执行状态',
					key: 'status',
					name: 'status',
					value: '',
					type: 'select',
					config: {
						mode: 'single-column',
						label: 'label',
						value: 'value'
					},
					list: vm.vuex_dict.execute_status,
					rules: [{
						required: true,
						message: '请选择执行状态',
						trigger: ['change', 'blur'],
					}]
				},
				{
					label: '执行措施',
					key: 'measures',
					name: 'measures',
					value: '',
					type: 'select',
					config: {
						mode: 'single-column',
						label: 'label',
						value: 'value'
					},
					list: vm.vuex_dict.execute_measures,
					rules: [{
						required: true,
						message: '请选择执行措施',
						trigger: ['change', 'blur'],
					}]
				},
				{
					label: '执行请求',
					key: 'executeRequest',
					name: 'executeRequest',
					value: '',
					type: 'input',
					config: {},
					rules: [{
						required: true,
						message: '请输入执行请求',
						trigger: ['change', 'blur'],
					}]
				},{
					label: '履行情况',
					key: 'performance',
					name: 'performance',
					value: '',
					type: 'input',
					config: {},
					rules: [{
						required: true,
						message: '请输入履行情况',
						trigger: ['change', 'blur'],
					}]
				}
				
			]
		}]
		vm.$data.formConfig = result
		resolve(result)
	})
}

export default formConfig
