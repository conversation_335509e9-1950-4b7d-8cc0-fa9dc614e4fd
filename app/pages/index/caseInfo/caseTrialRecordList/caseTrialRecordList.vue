<template>
	<view>
		<creatForm ref='creatForm' :formConfig='formConfig' @confirm='confirm' @submit='submit' />
	</view>
</template>

<script>
	import creatForm from '@/components/creatForm.vue'
	import formConfig from './formConfig.js'
	export default {
		components: {
			creatForm
		},
		data() {
			return {
				id:'',
				gid:'',
				formConfig: {},
				formObj: {},
				info:{},
			};
		},
		onLoad(e) {
			this.id = e.id
			this.gid = e.gid
			formConfig(this)
		},
		onShow() {
			let title = this.id?'编辑庭审记录':'新增庭审记录'
			uni.setNavigationBarTitle({
				title
			})
			this.allBriefList()
			if(this.id){
				this.caseTrialRecordInfo()
			}
		},
		methods:{
			// 开庭律师
			async allBriefList() {
				let res = await this.$u.api.lawCase.allBriefList()
				if (res) {
					this.formConfig[0].form[3].list = res.data
					// this.formConfig[0].form[3].list = res.treeData
				}
			},
			async caseTrialRecordInfo(){
				let res = await this.$u.api.lawCase.caseTrialRecordInfo(this.id)
				if(res){
					Object.assign(this.info,{
						...res.caseTrialRecord,
						openCourtType:this.vuex_dict.open_court_type.find(dict=>dict.value==res.caseTrialRecord.openCourtType)['label'],
						barrister:res.caseTrialRecord.barrister.name,
					})
					this.confirm({
						name:'openCourtType',
						label: this.vuex_dict.open_court_type.find(dict=>dict.value==res.caseTrialRecord.openCourtType)['label'],
						value: this.vuex_dict.open_court_type.find(dict=>dict.value==res.caseTrialRecord.openCourtType)['value'],
					})
					this.confirm({
						name:'barrister',
						label: this.formConfig[0].form[3].list.find(dict=>dict.name==res.caseTrialRecord.barrister.name)['name'],
						value: this.formConfig[0].form[3].list.find(dict=>dict.name==res.caseTrialRecord.barrister.name)['id'],
					})
					// this.confirm({
					// 	name:'executeStatus',
					// 	label: this.vuex_dict.execute_status.find(dict=>dict.value==res.casePropertyPreservation.executeStatus)['label'],
					// 	value: this.vuex_dict.execute_status.find(dict=>dict.value==res.casePropertyPreservation.executeStatus)['value'],
					// })
					this.formConfig[0].form.map(item=>{
						item['value'] = String(this.info[item.key])
						this.$refs['creatForm'].form[item.key] = String(this.info[item.key])
						return item
					})
				}
			},
			confirm(res) {
				this.formObj = {
					...this.formObj,
					[res['name']]: {
						id: res.value,
						name: res.label,
					}
				}
			},
			async submit(data){
				let res = this.$u.deepClone(data);
				let o = [
					'openCourtType',
				]
				Object.keys(this.formObj).forEach((key)=>{
					if(o.includes(key)){
						res[key] = this.formObj[key]['id']
					}
				})
				res['lawCase'] = {
					id:this.gid
				}
				res['id'] = this.id||''
				res['barrister'] = this.formObj.barrister
				res['openCourtDate'] = res['openCourtDate']?res['openCourtDate'] + ' 00:00:00':''
				console.log(res)
				let result = await this.$u.api.lawCase.saveCaseTrialRecord(res)
				if(result){
					this.$u.toast('提交成功')
					uni.navigateBack()
				}
			
				
			}
		}
	}
</script>

<style scoped lang="scss">

</style>
