const formConfig = function(vm) {
	return new Promise(async (resolve, rejict) => {
		let result = [{
			title: '庭审记录',
			key: 'concernPersonList',
			type: 'Array',
			add: false,
			form: [{
					label: '开庭日期',
					key: 'openCourtDate',
					name: 'openCourtDate',
					value: '',
					type: 'picker',
					config: {
						mode: 'time',
						params: {
							year: true,
							month: true,
							day: true,
						},
					},
					list: [],
					rules: [{
						required: true,
						message: '请选择日期',
						trigger: ['change', 'blur'],
					}]
				},{
					label: '开庭地址',
					key: 'openCourtAddress',
					name: 'openCourtAddress',
					value: '',
					type: 'input',
					config: {},
					rules: [{
						required: true,
						message: '请输入开庭地址',
						trigger: ['change', 'blur'],
					}]
				},
				{
					label: '开庭类型',
					key: 'openCourtType',
					name: 'openCourtType',
					value: '',
					type: 'select',
					config: {
						mode: 'single-column',
						label: 'label',
						value: 'value'
					},
					list: vm.vuex_dict.open_court_type,
					rules: [{
						required: true,
						message: '请选择开庭类型',
						trigger: ['change', 'blur'],
					}]
				},
				{
					label: '出庭律师',
					key: 'barrister',
					name: 'barrister',
					value: '',
					type: 'select',
					config: {
						mode: 'single-column',
						label: 'name',
						value: 'id'
					},
					list: [],
					rules: [{
						required: true,
						message: '请选择出庭律师',
						trigger: ['change', 'blur'],
					}]
				},
				{
					label: '争议焦点',
					key: 'controversyFocus',
					name: 'controversyFocus',
					value: '',
					type: 'input',
					config: {},
					rules: [{
						required: true,
						message: '请输入争议焦点',
						trigger: ['change', 'blur'],
					}]
				},
				{
					label: '我方意见',
					key: 'ourOpinion',
					name: 'ourOpinion',
					value: '',
					type: 'input',
					config: {},
					rules: [{
						required: true,
						message: '请输入我方意见',
						trigger: ['change', 'blur'],
					}]
				},{
					label: '对方意见',
					key: 'otherOpinion',
					name: 'otherOpinion',
					value: '',
					type: 'input',
					config: {},
					rules: [{
						required: true,
						message: '请输入对方意见',
						trigger: ['change', 'blur'],
					}]
				},{
					label: '法官态度',
					key: 'judgeAttitude',
					name: 'judgeAttitude',
					value: '',
					type: 'input',
					config: {},
					rules: [{
						required: true,
						message: '请输入法官态度',
						trigger: ['change', 'blur'],
					}]
				},{
					label: '庭审总结',
					key: 'trialSummary',
					name: 'trialSummary',
					value: '',
					type: 'input',
					config: {},
					rules: [{
						required: true,
						message: '请输入庭审总结',
						trigger: ['change', 'blur'],
					}]
				},
				
			]
		}]
		vm.$data.formConfig = result
		resolve(result)
	})
}

export default formConfig
