<template>
	<view class="content">
		<view class="u-flex-1">
			<u-modal @confirm='confirm' v-model="show" content="是否删除承办人？" show-cancel-button></u-modal>
			<u-cell-group v-if="info.name">
				<u-cell-item title="姓名" :arrow="false" :value="info.name"></u-cell-item>
				<u-cell-item title="联系方式" :arrow="false" :value="info.phone"></u-cell-item>
				<u-cell-item title="科室" :arrow="false" :value="info.department"></u-cell-item>
				<u-cell-item title="职务" :arrow="false" :value="info.post"></u-cell-item>
				<u-cell-item title="地址" :arrow="false" :value="info.address"></u-cell-item>
			</u-cell-group>
		</view>
		<view class="u-padding-30">
			<u-button type="primary" @click="goPage">编辑</u-button>
			<u-button class="u-margin-top-30" type="error" @click="show=true">删除</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id:'',
				gid:'',
				info:'',
				show:false
			};
		},
		onLoad(e) {
			this.gid = e.gid
			this.id = e.id
		},
		onShow() {
			this.caseUndertakePersonInfo()
		},
		methods:{
			goPage(){
				uni.navigateTo({
					url:`../editPerson/editPerson?id=${this.id}&gid=${this.gid}`
				})
			},
			async caseUndertakePersonInfo(){
				let res = await this.$u.api.lawCase.caseUndertakePersonInfo(this.id)
				if(res){
					this.info = res.caseUndertakePerson
				}
			},
			async confirm(){
				let res = await this.$u.api.lawCase.delCaseUndertakePerson(this.id)
				if(res){
					uni.navigateBack()
					this.$u.toast('删除成功')
				}
			}
		}
	}
</script>

<style lang="scss">

</style>
