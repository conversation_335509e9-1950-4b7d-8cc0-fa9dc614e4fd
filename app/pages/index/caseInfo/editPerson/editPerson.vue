<template>
	<view>
		<creatForm ref='creatForm' :formConfig='formConfig' @confirm='confirm' @submit='submit' />
	</view>
</template>

<script>
	import creatForm from '@/components/creatForm.vue'
	import formConfig from './formConfig.js'
	export default {
		components: {
			creatForm
		},
		data() {
			return {
				id:'',
				gid:'',
				formConfig: {},
				formObj: {},
				info:{},
			};
		},
		onLoad(e) {
			this.id = e.id
			this.gid = e.gid
			formConfig(this)
		},
		onShow() {
			this.caseUndertakePersonInfo()
		},
		methods:{
			async caseUndertakePersonInfo(){
				let res = await this.$u.api.lawCase.caseUndertakePersonInfo(this.id)
				if(res){
					Object.assign(this.info,{
						...res.caseUndertakePerson,
					})
					this.formConfig[0].form.map(item=>{
						item['value'] = this.info[item.key]
						this.$refs['creatForm'].form[item.key] = this.info[item.key]
					})
					this.formConfig[0].key = +new Date()
				}
			},
			confirm(res) {
				this.formObj = {
					...this.formObj,
					[res['name']]: {
						id: res.value,
						name: res.label,
					}
				}
			},
			async submit(data){
				let res = this.$u.deepClone(data);
				res['lawCase.id'] = this.gid
				res['id'] = this.id
				
				let result = await this.$u.api.lawCase.saveCaseUndertakePerson(res)
				if(result){
					this.$u.toast('提交成功')
					uni.navigateBack()
				}
			}
		}
	}
</script>

<style scoped lang="scss">

</style>
