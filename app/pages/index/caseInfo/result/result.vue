<template>
	<view>
		<creatForm ref='creatForm' :formConfig='formConfig' @confirm='confirm' @submit='submit' />
	</view>
</template>

<script>
	import creatForm from '@/components/creatForm.vue'
	import formConfig from './formConfig.js'
	export default {
		components: {
			creatForm
		},
		data() {
			return {
				id:'',
				formConfig: {},
				formObj: {},
				info:{},
			};
		},
		onLoad(e) {
			this.id = e.id
			formConfig(this)
		},
		onShow() {
			this.detailInfo()
		},
		methods:{
			async detailInfo(){
				let res = await this.$u.api.lawCase.detailInfo(this.id)
				if(res){
					let data = res.data.lawCase
					Object.assign(this.info,{
						...data,
						entrustDate:data.entrustDate?this.$u.timeFormat(data.entrustDate, 'yyyy-mm-dd'):'',
						recordDate:data.recordDate?this.$u.timeFormat(data.recordDate, 'yyyy-mm-dd'):'',
						rulingDate:data.rulingDate?this.$u.timeFormat(data.rulingDate, 'yyyy-mm-dd'):'',
						settleCaseDate:data.settleCaseDate?this.$u.timeFormat(data.settleCaseDate, 'yyyy-mm-dd'):'',
						type:data.type?this.vuex_dict.case_type.find(dict=>dict.value==data.type)['label']:'',
						chargeMode:data.chargeMode?this.vuex_dict.charge_mode.find(dict=>dict.value==data.chargeMode)['label']:'',
						acceptUnitType:data.acceptUnitType?this.vuex_dict.accept_unit_type.find(dict=>dict.value==data.acceptUnitType)['label']:'',
						trialResult:data.trialResult?this.vuex_dict.trial_result.find(dict=>dict.value==data.trialResult)['label']:'',
						settleCaseStatus:data.settleCaseStatus?this.vuex_dict.settle_case_status.find(dict=>dict.value==data.settleCaseStatus)['label']:'',
					})
					let config = [
						{name:'type',dict:'case_type'},
						{name:'chargeMode',dict:'charge_mode'},
						{name:'acceptUnitType',dict:'accept_unit_type'},
						{name:'trialResult',dict:'trial_result'},
						{name:'settleCaseStatus',dict:'settle_case_status'},
					]
					config.forEach(item=>{
						if(data[item['name']]){
							this.confirm({
								name:item['name'],
								label: this.vuex_dict[item.dict].find(dict=>dict.value==data[item['name']])['label'],
								value: this.vuex_dict[item.dict].find(dict=>dict.value==data[item['name']])['value'],
							})
						}
					})
					
					this.confirm({
						name:'caseProgram',
						label: data.caseProgram.name,
						value: data.caseProgram.id,
					})
					this.confirm({
						name:'caseCause',
						label: data.caseCause.name,
						value: data.caseCause.id,
					})
					// this.confirm({
					// 	name:'caseCause',
					// 	label: data.caseCause.name,
					// 	value: data.caseCause.id,
					// })
					let arr = ['caseProgram','caseCause','hostUser','caseProgram','caseProgram',]
					this.formConfig.map(i=>{
						i.form.map(item=>{
							if (arr.includes(item.key)) {
								item['value'] = this.info[item.key].name||''
								this.$refs['creatForm'].form[item.key] = this.info[item.key].name||''
							} else {
								item['value'] = this.info[item.key]||''
								this.$refs['creatForm'].form[item.key] = String(this.info[item.key]||'')
							}
						})
					})
					
				}
			},
			confirm(res) {
				this.formObj = {
					...this.formObj,
					[res['name']]: {
						id: res.value,
						name: res.label,
					}
				}
			},
			async submit(data){
				let res = this.$u.deepClone(data);
				let o = [
					'acceptUnitType',
					'trialResult',
				]
				Object.keys(this.formObj).forEach((key)=>{
					if(o.includes(key)){
						res[key] = this.formObj[key]['id']
					}
				})
				res['id'] = this.id
				console.log(res,'提交的数据')
				let result = await this.$u.api.lawCase.saveAccept(res)
				if(result){
					this.$u.toast('提交成功')
					uni.navigateBack()
				}
			}
		}
	}
</script>

<style scoped lang="scss">

</style>
