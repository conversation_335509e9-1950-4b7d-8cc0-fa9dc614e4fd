const formConfig = function(vm) {
	return new Promise(async (resolve, rejict) => {
		let result = [
			{
				title: '受理情况',
				key: 'concernPersonList',
				type: 'Array',
				add: false,
				form: [{
							label: '地区',
							key: 'acceptUnitArea',
							name: 'acceptUnitArea',
							value: '',
							type: 'input',
							config: {},
							rules: [{
								required: true,
								message: '请输入地区',
								trigger: ['change', 'blur'],
							}]
						},
						{
							label: '单位类型',
							key: 'acceptUnitType',
							name: 'acceptUnitType',
							value: '',
							type: 'select',
							config: {
								mode: 'single-column',
								label: 'label',
								value: 'value'
							},
							list: vm.vuex_dict.accept_unit_type,
							rules: [{
								required: true,
								message: '请选择单位类型',
								trigger: ['change', 'blur'],
							}]
						},
						{
							label: '受理单位',
							key: 'acceptUnitName',
							name: 'acceptUnitName',
							value: '',
							type: 'input',
							config: {},
							rules: [{
								required: true,
								message: '请输入受理单位',
								trigger: ['change', 'blur'],
							}]
						},
						{
							label: '立案日期',
							key: 'recordDate',
							name: 'recordDate',
							value: '',
							type: 'picker',
							config: {
								mode: 'time',
								params: {
									year: true,
									month: true,
									day: true,
								},
							},
							list: [],
							rules: [{
								required: true,
								message: '请选择立案日期',
								trigger: ['change', 'blur'],
							}]
						},
						{
							label: '裁决日期',
							key: 'rulingDate',
							name: 'rulingDate',
							value: '',
							type: 'picker',
							config: {
								mode: 'time',
								params: {
									year: true,
									month: true,
									day: true,
								},
							},
							list: [],
							rules: [{
								required: true,
								message: '请选择裁决日期',
								trigger: ['change', 'blur'],
							}]
						},
						{
							label: '审理结果',
							key: 'trialResult',
							name: 'trialResult',
							value: '',
							type: 'select',
							config: {
								mode: 'single-column',
								label: 'label',
								value: 'value'
							},
							list: vm.vuex_dict.trial_result,
							rules: [{
								required: true,
								message: '请选择审理结果',
								trigger: ['change', 'blur'],
							}]
						},
				]
			}
		]
		vm.$data.formConfig = result
		resolve(result)
	})
}

export default formConfig
