<template>
	<view class="content">
		<view class="u-flex-1">
			<u-modal @confirm='confirm' v-model="show" content="是否删除当事人？" show-cancel-button></u-modal>
			<u-cell-group v-if="info.name">
				<u-cell-item title="姓名" :arrow="false" :value="info.name"></u-cell-item>
				<u-cell-item title="联系方式" :arrow="false" :value="info.phone"></u-cell-item>
				<u-cell-item title="委托方" :arrow="false" :value="vuex_dict.yes_no.find(dict=>dict.value==info.isEntrust)['label']"></u-cell-item>
				<u-cell-item title="当事人类型" :arrow="false" :value="vuex_dict.customer_type.find(dict=>dict.value==info.type)['label']"></u-cell-item>
				<u-cell-item title="属性" :arrow="false" :value="info.attribute"></u-cell-item>
			</u-cell-group>
		</view>
		<view class="u-padding-30">
			<u-button type="primary" @click="goPage">编辑</u-button>
			<u-button class="u-margin-top-30" type="error" @click="show=true">删除</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id:'',
				gid:'',
				info:'',
				show:false
			};
		},
		onLoad(e) {
			this.gid = e.gid
			this.id = e.id
		},
		onShow() {
			this.LitigantInfo()
		},
		methods:{
			goPage(){
				uni.navigateTo({
					url:`../editLitigant/editLitigant?id=${this.id}&gid=${this.gid}`
				})
			},
			async LitigantInfo(){
				let res = await this.$u.api.lawCase.LitigantInfo(this.id)
				if(res){
					this.info = res.caseConcernPerson
				}
			},
			async confirm(){
				let res = await this.$u.api.lawCase.deleteLitigant(this.id)
				if(res){
					uni.navigateBack()
					this.$u.toast('删除成功')
				}
			}
		}
	}
</script>

<style lang="scss">

</style>
