const formConfig = function(vm) {
	return new Promise(async (resolve, rejict) => {
		let result = [
			{
				title: '案件审核',
				key: 'concernPersonList',
				type: 'Array',
				add: false,
				form: [
					{
						label: '审批意见',
						key: 'reason',
						name: 'reason',
						value: '',
						type: 'input',
						config: {
							type:'textarea'
						},
						rules: [{
							required: true,
							message: '请输入审批意见',
							trigger: ['change', 'blur'],
						}]
					},
					{
						label: '审核结果',
						key: 'result',
						name: 'result',
						value: '',
						type: 'select',
						config: {
							mode: 'single-column',
							label: 'label',
							value: 'value'
						},
						list: [
							{
								label:'终止案件',
								value:'0'
							},
							{
								label:'通过',
								value:'1'
							},
							{
								label:'拒绝',
								value:'2'
							},
						],
						rules: [{
							required: true,
							message: '审核结果',
							trigger: ['change', 'blur'],
						}]
					},
				]
			}
		]
		vm.$data.formConfig = result
		resolve(result)
	})
}

export default formConfig
