<template>
	<view>
		<creatForm ref='creatForm' :formConfig='formConfig' @confirm='confirm' @submit='submit' />
	</view>
</template>

<script>
	import creatForm from '@/components/creatForm.vue'
	import formConfig from './formConfig.js'
	export default {
		components: {
			creatForm
		},
		data() {
			return {
				id:'',
				formConfig: {},
				formObj: {}
			};
		},
		onLoad(e) {
			this.id = e.id
			formConfig(this)
		},
		methods:{
			confirm(res) {
				this.formObj = {
					...this.formObj,
					[res['name']]: {
						id: res.value,
						name: res.label,
					}
				}
			},
			async submit(data){
				let res = this.$u.deepClone(data);
				let o = [
					'result',
				]
				Object.keys(this.formObj).forEach((key)=>{
					res[key] = this.formObj[key]
					if(o.includes(key)){
						res[key] = this.formObj[key]['id']
					}
				})
				res['id'] = this.id
				console.log(res)
				let result = await this.$u.api.lawCase.auditCase(res)
				if(result){
					this.$u.toast('提交成功')
					uni.navigateBack()
				}
			}
		}
	}
</script>

<style scoped lang="scss">

</style>
