import baseUrl from '@/utils/config.js'
let url = process.env.NODE_ENV=='development'?'/law':baseUrl
let httpUrl = [
	'/law/lawcase/case/saveInfo',
	'/law/case/caseConcernPerson/save',
	'/law/app/lawcase/customer/save',
	'/law/lawcase/customer/save',
	'/law/lawcase/case/save',
	'/law/case/caseHandleStrategy/save',
	'/law/case/casePropertyPreservation/save',
	'/law/case/caseTrialRecord/save',
	'/law/case/caseExecuteSituation/save',
	'/law/lawcase/todoInfo/save',
	'/law/lawcase/financeFlowRecord/saveInfo',
]
// 这里的vm，就是我们在vue文件里面的this，所以我们能在这里获取vuex的变量，比如存放在里面的token变量
const install = (Vue, vm) => {
	// 此为自定义配置参数，具体参数见上方说明
	Vue.prototype.$u.http.setConfig({
		originalData: true,
		// baseUrl: 'http://192.168.1.85:9100',
		// baseUrl: 'http://192.168.1.74:9100',
		baseUrl:((vm.$u.os())!='ios')&&baseUrl || url,
		loadingText: '努力加载中~',
		loadingTime: 800,
		// ......
	});
	
	// 请求拦截，配置Token等参数
	Vue.prototype.$u.http.interceptor.request = (config) => {
		// 方式一，存放在vuex的token，假设使用了uView封装的vuex方式
		// 见：https://uviewui.com/components/globalVariable.html
		// config.header.token = vm.token;
		// 方式二，如果没有使用uView封装的vuex方法，那么需要使用$store.state获取
		// config.header.token = vm.$store.state.token;
		
		// 方式三，如果token放在了globalData，通过getApp().globalData获取
		// config.header.token = getApp().globalData.username;
		
		// 方式四，如果token放在了Storage本地存储中，拦截是每次请求都执行的
		// 所以哪怕您重新登录修改了Storage，下一次的请求将会是最新值
		// const token = uni.getStorageSync('token');
		// config.header.token = token;
		console.log(config,'config')
		if(config.method!="GET") config.header={'Content-Type': 'application/x-www-form-urlencoded;'}
		if(httpUrl.includes(config.url)) config.header={};
		config.header.token = vm.vuex_token;
		// 可以对某个url进行特别处理，此url参数为this.$u.get(url)中的url值
		// 最后需要将config进行return
		return config;
		// 如果return一个false值，则会取消本次请求
		// if(config.url == '/user/rest') return false; // 取消某次请求
	}
	
	// 响应拦截，判断状态码是否通过
	Vue.prototype.$u.http.interceptor.response = (res) => {
		if(res.statusCode == 200) {
			if(res.data.success){
				return res.data;
			}else{
				vm.$u.toast(res.data.msg);
				return false;
			}
			// res为服务端返回值，可能有code，result等字段
			// 这里对res.result进行返回，将会在this.$u.post(url).then(res => {})的then回调中的res的到
			// 如果配置了originalData为true，请留意这里的返回值
		}else if(res.statusCode == 401||res.statusCode == 402) {
			// 假设201为token失效，这里跳转登录
			vm.$u.toast('验证失败，请重新登录');
			vm.$u.vuex('vuex_token', '');
			vm.$u.vuex('vuex_user', '');
			vm.$u.vuex('vuex_dict', []);
			// setTimeout(() => {
			// 	// 此为uView的方法，详见路由相关文档
			vm.$u.route('/pages/login/login')
			// }, 1500)
			return false;
		} else {
			vm.$u.toast(res.data.msg);
			// 如果返回false，则会调用Promise的reject回调，
			// 并将进入this.$u.post(url).then().catch(res=>{})的catch回调中，res为服务端的返回值
			return false;
		}
	}
}

export default {
	install
}