<template>
	<view class="tree-item">
		<u-popup v-model="showFile" mode="right">
				<view class="pop_content">
					<view  @click="open(item.fullPath,item.fileType)" style="width: 100%;" v-for="(item,key) in fileList" :key='key'>
						<u-cell-item :value="item.name" :arrow="false">
							<template #icon>
								<image v-if="imgType.includes(item.fileType)" style="width: 80rpx;height: 80rpx;"
																				:src="item.fullPath" mode=""></image>
								<image v-if="docType.includes(item.fileType)" style="width: 50rpx;height: 50rpx;" src="/static/img/wd.png" mode="">
								</image>
							</template>
						</u-cell-item>
					</view>
				</view>
			</u-popup>
		<view class="head" @click.stop="changeShow">
			<image :src="show?jian:add"  mode="scaleToFill" class="left-icon" 
				v-if="item[defaultProps.children] && item[defaultProps.children].length > 0"></image>
			<text class="txt">{{item[defaultProps.label]}}</text>
			<text v-if="item.isHaveFile==1" @click.stop="checkFile(item)" class="txt t_box" style="margin-left: 25rpx;color: #3F51B5;">(查看附件)</text>
			<text v-if="item.createBy" @click.stop="goPage(item)" class="txt t_box" style="margin-left: 25rpx;color: #3F51B5;">编辑</text>
		</view>
		<view v-if="item.content" class="main_box u-margin-left-40" style="font-size: 25rpx;">{{item.content}}</view>
		<view class="content" 
			v-if="item[defaultProps.children] && item[defaultProps.children].length > 0"
			v-show="show">
			<tree-node v-for="sitem in item[defaultProps.children]" 
				:item="sitem" 
				:key="sitem[defaultProps.id]" :defaultProps="defaultProps"></tree-node>
		</view>
	</view>
</template>

<script>
	import add from '../../../static/add.png'
	import jian from '../../../static/jian.png'
	export default {
		name: 'TreeNode',
		componentName: 'TreeNode',
		props: {
			item: {
				type: Object,
				default: () => {
					return {}
				}
			}
		},
		inject: ['defaultProps', 'onClickItem'],
		data() {
			return {
				add,jian,
				show: false,
				showFile:false,
				fileList:[],
				imgType: ['png', 'jpg', 'jpeg'],
				docType: ['docx', 'doc','pdf'],
			}
		},
		methods: {
			goPage(item){
				uni.navigateTo({
					url:`/pages/index/caseInfo/addRecords/addRecords?gid=${item.relevanceId}&id=${item.id}`
				})
			},
			open(path, type) {
							console.log(path, type)
							if (this.imgType.includes(type)) {
								var urls = [path]
								uni.previewImage({
									current: 0,
									urls //预览图片的地址，必须要数组形式，如果不是数组形式就转换成数组形式就可以
								})
							} else {
								uni.downloadFile({
									url: path,
									success: (res) => {
										if (res.statusCode === 200) {
											uni.openDocument({
												filePath: res.tempFilePath,
												// 如果文件名包含中文，建议使用escape(res.tempFilePath)转码，防止ios和安卓客户端导致的差异
												success: function(res) {
													console.log('打开文档成功');
												}
											});
										}
									}
								})
							}
						},
			async checkFile(e){
				let res = await this.$u.api.calendar.fileList(e.id)
				if(res){
					if(res.list.length>0){
						this.showFile = true
						this.fileList = res.list
					}else{
						this.$u.toast('暂无附件')
					}
				}
				console.log(e)
			},
			changeShow() {
				this.onClickItem(this.item);
				if (this.item[this.defaultProps.children] && this.item[this.defaultProps.children].length > 0) {
					this.show = !this.show;
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	@mixin animate2 {
	    -moz-transition: all .2s linear;
	    -webkit-transition: all .2s linear;
	    -o-transition: all .2s linear;
	    -ms-transition: all .2s linear;
	    transition: all .2s linear;
	}
	.pop_content{
		width: 400rpx;
	}
	.main_box{
		border: 1px solid #ccc;
		padding: 15rpx;
	}
	.tree-item{
		.head{
			display: flex;
			align-items: center;
			line-height: 60rpx;
			.txt{
				font-size: 25rpx;
				color: #222;
			}
			.t_box{
				display: inline-block;
				width: 120rpx;
			}
		}
		.left-icon{
			width: 40rpx;
			height: 40rpx;
			@include animate2;
			// transform: rotate(-90deg);
			// -ms-transform:rotate(-90deg);
			// -moz-transform:rotate(-90deg);
			// -webkit-transform: rotate(-90deg);
			// -o-transform:rotate(-90deg);
			&.rt45{
				transform: rotate(0deg);
				-ms-transform:rotate(0deg);
				-moz-transform:rotate(0deg);
				-webkit-transform: rotate(0deg);
				-o-transform:rotate(0deg);
			}
		}
		.content{
			padding-left: 40rpx;
		}
	}
	
</style>
