{
	"easycom": {
		"^u-(.*)": "@/uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/login/login",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle":"custom"
			}
		},
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle":"custom"
			}
		},
		{
			"path": "pages/center/index",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle":"custom"
			}
		}, {
			"path": "pages/calendar/calendar",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": true,
				"navigationStyle":"custom"
			}

		}, {
			"path": "pages/document/document",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle":"custom",
				"enablePullDownRefresh": false
			}

		}
	    ,{
            "path" : "pages/center/setting/setting",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "个人中心",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/addCase/addCase",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "新建案件",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/addPerson/addPerson",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "新建客户",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/caseInfo/caseInfo",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "案件详情",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/caseInfo/addLitigant/addLitigant",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "新建当事人",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/caseInfo/litigantInfo/litigantInfo",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "当事人详情",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/caseInfo/editLitigant/editLitigant",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "编辑当事人",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/personInfo/personInfo",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "客户详情",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/personInfo/editPerson/editPerson",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "编辑客户",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/caseInfo/info/info",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "案件详情",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/caseInfo/info/editCase/editCase",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "编辑案情",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/caseInfo/info/addCaseHandleStrategy/addCaseHandleStrategy",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "新增策略",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/caseInfo/result/result",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "受理情况",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/caseInfo/addPerson/addPerson",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "添加承办人",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/caseInfo/personInfo/personInfo",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "承办人详情",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/caseInfo/editPerson/editPerson",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "编辑承办人",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/caseInfo/casePropertyPreservationList/casePropertyPreservationList",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "财产保全",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/caseInfo/caseTrialRecordList/caseTrialRecordList",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "庭审记录",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/caseInfo/caseExecuteSituationList/caseExecuteSituationList",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "执行情况",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/caseInfo/phase/phase",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "阶段管理",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/calendar/list/list",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "待办事项",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/caseInfo/addRecords/addRecords",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/center/setting/password/password",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "修改密码",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/center/setting/info/info",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "个人信息",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/audit/audit",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "案件审核",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/caseInfo/records/records",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "查看记录",
                "enablePullDownRefresh": false
            }
            
        }
        ,{
            "path" : "pages/index/caseInfo/finance/finance",
            "style" :                                                                                    
            {
                "navigationBarTitleText": "财务流水",
                "enablePullDownRefresh": false
            }
            
        }
    ],
	"globalStyle": {
		// "app-plus": {
		// 	"titleNView": false
		// },
		// "navigationStyle":"custom",
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "案件",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#FFFFFF"
	},
	"tabBar": {
		"color": "#909399",
		"selectedColor": "#3F51B5",
		"borderStyle": "black",
		"backgroundColor": "#ffffff",
		"list": [{
			"pagePath": "pages/index/index",
			"iconPath": "static/img/tabbar/sy1.png",
			"selectedIconPath": "static/img/tabbar/sy.png",
			"text": "首页"
		}, 
		// {
		// 	"pagePath": "pages/document/document",
		// 	"iconPath": "static/img/tabbar/wd1.png",
		// 	"selectedIconPath": "static/img/tabbar/wd.png",
		// 	"text": "文档"
		// }, 
		{
			"pagePath": "pages/calendar/calendar",
			"iconPath": "static/img/tabbar/rl1.png",
			"selectedIconPath": "static/img/tabbar/rl.png",
			"text": "日程"
		}, {
			"pagePath": "pages/center/index",
			"iconPath": "static/img/tabbar/gr1.png",
			"selectedIconPath": "static/img/tabbar/gr.png",
			"text": "个人中心"
		}]
	},
	"condition" : { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [
			{
				"name": "", //模式名称
				"path": "", //启动页面，必选
				"query": "" //启动参数，在页面的onLoad函数里面得到
			}
		]
	}
}
