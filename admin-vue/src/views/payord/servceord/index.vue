<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="租户id" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入租户id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
       
      <el-form-item label="支付方式" prop="payWay">
      <el-select v-model="queryParams.payWay" placeholder="请选择支付方式" clearable size="small">
       <el-option  label="全部" value=""  />
         <el-option  label="微信" value="1"  />
		 <el-option  label="支付宝" value="2"  />
          <el-option  label="线下" value="3"  />
       </el-select>
      </el-select>
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
         <el-option  label="全部" value=""  />
		  <el-option  label="待支付" value="0"  />
		  <el-option  label="待审核" value="1"  />
		   <el-option  label="已支付" value="2"  />
		   <el-option  label="已失败" value="3"  />
         </el-select>
        </el-select>
      </el-form-item>
     
      <el-form-item label="订单号" prop="ordId">
        <el-input
          v-model="queryParams.ordId"
          placeholder="请输入订单id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
       
     
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
       
        
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['mall:ord:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="ordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
	     <el-table-column label="订单号" align="center" prop="ordId"  width="150" />
      <el-table-column label="租户id" align="center" prop="userId" />
      <el-table-column label="订单金额" align="center" prop="amount" /> 
      <el-table-column label="支付方式"  :formatter="payWayFormat" align="center" prop="payWay" /> 
      <el-table-column label="状态" align="center" prop="status"  :formatter="statusFormat"/>
	    <el-table-column label="月数" align="center" prop="months" />
	  <el-table-column label="月数金额" align="center" prop="monthsAmount" />
     <el-table-column label="人数" align="center" prop="mans" />
	   <el-table-column label="人数金额" align="center" prop="mansAmount" />
	       <el-table-column label="模板类型"  :formatter="modelTypeFormat" align="center" prop="man" /> 
      <el-table-column label="支付时间" align="center" prop="payTime" width="180"> 
      </el-table-column>
       
	 <el-table-column label="后台备注" align="center" prop="adminRemark" />
	 <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
	     <template slot-scope="scope">
	       
	       <el-button
	         size="mini"
	         type="text" 
	      icon="el-icon-edit"
	       @click="updateRemark(scope.row)"
	       >修改备注</el-button>
	     </template>
	   </el-table-column>
	 </el-table>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 修改备注对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="订单号" prop="ordId">
          <el-input disabled v-model="form.ordId"  />
        </el-form-item>
         
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOrd, getOrd,updateRemark, exportOrd } from "@/api/mall/ord";

export default {
  name: "Ord",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单表格数据
      ordList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10, 
        source: 5
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询订单列表 */
    getList() {
      this.loading = true;
      listOrd(this.queryParams).then(response => {
        this.ordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        
        remark: null, 
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    } ,
	//
	statusFormat(row, column) {
	   var status= row.status;
	   if(status==0){
		   return "待支付"
	   }else if(status==1){
		   return "待审核";
	   }else if(status==2){
		   return "支付成功";
	   }else if(status==3){
		   return "已失败";
	   }
	},
	payWayFormat(row, column) {
	   var payWay= row.payWay;
	   if(payWay==1){
		   return "微信支付"
	   }else if(payWay==2){
		   return "支付宝";
	   }else if(payWay==3){
		   return "线下支付";
	   }else{
		   return "--";
	   }  
	},
	modelTypeFormat(row, column) {
	   var nums= row.nums;
	   if(nums==1){
		   return "民事模板"
	   }else if(nums==2){
		   return "刑事模板";
	   }else if(nums==3){
		   return "行政模板";
	   }else if(nums==4){
		   return "其他模板";
	   } else if(nums==5){
		   return "所有模板";
	   } else if(nums==6){
		   return "民事+刑事模板";
	   }  else if(nums==7){
		   return "民事+行政模板";
	   } else if(nums==8){
		   return "刑事+行政模板";
	   }   else if(nums==9){
		   return "民事+民事+刑事模板";
	   }     
	   return "";
	},
	sourceFormat(row, column) {
	   var source= row.source;
	   if(source==1){
		   return "购买vip"
	   }else if(status==2){
		   return "续费vip";
	   }else if(status==3){
		   return "购买人数";
	   }else if(status==4){
		   return "购买模板";
	   }else if(status==5){
		   return "购买vip服务";
	   }   
	},
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有订单数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportOrd(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    },
    /** 修改按钮操作 */
    updateRemark(row) {
      this.reset();
      const id = row.id || this.ids
     this.form.remark=row.adminRemark;
     this.form.id=row.id;
	  this.form.ordId=row.ordId;
       this.open = true;
       this.title = "修改订单后台备注"; 
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) { 
			console.log(this.form);
            updateRemark(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
         
        }
      });
    }
  
  }
};
</script>
