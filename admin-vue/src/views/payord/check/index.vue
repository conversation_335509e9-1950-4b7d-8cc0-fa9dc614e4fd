<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="租户id" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入租户id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
       
      <el-form-item label="类型" prop="source">
        <el-select v-model="queryParams.source" placeholder="请选择类型" clearable size="small">
         <el-option  label="全部" value=""  /> 
      		  <el-option  label="购买vip" value="1"  />
      		   <el-option  label="续费vip" value="2"  />
      		   <el-option  label="购买人数" value="3"  />
			  <el-option  label="购买模板" value="4"  />
			   <el-option  label="购买VIP服务" value="5"  />
         </el-select>
        </el-select>
      </el-form-item>
       
     
      <el-form-item label="订单号" prop="ordId">
        <el-input
          v-model="queryParams.ordId"
          placeholder="请输入订单id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
       
     
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
       
        
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['mall:ord:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="ordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"  />
	    <el-table-column label="订单号" align="center" prop="ordId" /> 
	    <el-table-column label="类型" align="center" prop="source" :formatter="sourceFormat" />
      <el-table-column label="租户id" align="center" prop="userId" />
      <el-table-column label="订单金额" align="center" prop="amount" />   
	    <el-table-column label="数量" align="center" prop="nums" :formatter="numsFormat" />
      <el-table-column label="备注" align="center" prop="remark" />
    
		 <el-table-column label="提交时间" align="center" prop="createTime" width="180"> 
		 </el-table-column>
		<el-table-column label="操作" align="center" class-name="small-padding fixed-width">
		  <template slot-scope="scope">
		    
		    <el-button
		      size="mini"
		      type="text" 
		   icon="el-icon-edit"
		    @click="checkWin(scope.row)"
		    >审核</el-button>
		  </template>
		</el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

 <!-- 审核对话框 -->
	    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
	      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
	        <el-form-item label="订单号" prop="ordId">
	          <el-input disabled v-model="form.ordId"  />
	        </el-form-item>
			  
	        <el-form-item label="备注" prop="remark">
	          <el-input type="textarea" v-model="form.remark" placeholder="请输入审核备注" />
	        </el-form-item>
	      </el-form>
	      <div slot="footer" class="dialog-footer">
	        <el-button type="primary" @click="ok">通过</el-button>
			<el-button type="danger" @click="refuse">拒绝</el-button>
	        <el-button @click="cancel">取 消</el-button>
	      </div>
	    </el-dialog>
    
  </div>
</template>

<script>
import { listOrd, getOrd,ordCheck,  exportOrd } from "@/api/mall/ord";

export default {
  name: "Ord",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单表格数据
      ordList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10, 
		status:1,
        payWay: 3
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询订单列表 */
    getList() {
      this.loading = true;
      listOrd(this.queryParams).then(response => {
        this.ordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null, 
        ordId: null,
        remark: null, 
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    } ,
	//
	statusFormat(row, column) {
	   var status= row.status;
	   if(status==0){
		   return "待支付"
	   }else if(status==1){
		   return "待审核";
	   }else if(status==2){
		   return "支付成功";
	   }else if(status==3){
		   return "已失败";
	   }
	},
	payWayFormat(row, column) {
	   var payWay= row.payWay;
	   if(payWay==1){
		   return "微信支付"
	   }else if(status==2){
		   return "支付宝";
	   }else if(status==3){
		   return "线下支付";
	   } 
	},
	sourceFormat(row, column) {
	   var source= row.source;
	   if(source==1){
		   return "购买vip"
	   }else if(source==2){
		   return "续费vip";
	   }else if(source==3){
		   return "购买人数";
	   }else if(source==4){
		   return "购买模板";
	   }else if(source==5){
		   return "购买vip服务";
	   }  
	},
	numsFormat(row, column) {
	   var source= row.source;
	   if(source==1){
		   return row.nums+"月"
	   }else if(source==2){
		  return row.nums+"月"
	   }else if(source==3){
		   return row.nums+"人"
	   }else if(source==4){
		  var nums= row.nums;
		  if(nums==1){
		  		   return "民事模板"
		  }else if(nums==2){
		  		   return "刑事模板";
		  }else if(nums==3){
		  		   return "行政模板";
		  }else if(nums==4){
		  		   return "其他模板";
		  } else if(nums==5){
		  		   return "所有模板";
		  }  
	   }else if(source==5){
		   var sms=row.months+"月_"+row.mans+"人_";
		    var nums= row.nums;
		   if(nums==1){
		   		   sms+="民事模板"
		   }else if(nums==2){
		   	  sms+="刑事模板"
		   }else if(nums==3){
		   	  sms+="行政模板"
		   }else if(nums==4){
		   	  sms+="其他模板"
		   } else if(nums==5){
		   	  sms+="所有模板"
		   }  else if(nums==6){
				   sms+= "民事+刑事模板";
			   }  else if(nums==7){
				   sms+= "民事+行政模板";
			   } else if(nums==8){
				   sms+= "刑事+行政模板";
			   }   else if(nums==9){
				   sms+= "民事+民事+刑事模板";
			   }     
			return sms;
	   }   
	},
	
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有订单数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportOrd(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    },
    /** 审核按钮操作 */
    checkWin(row) {
      this.reset();
      const id = row.id || this.ids
      this.form.ordId=row.ordId;
	  this.form.id=row.id;
        this.open = true;
        this.title = "线下订单审核"; 
    },
    /** 通过按钮 */
    ok() {
      this.$refs["form"].validate(valid => {
        if (valid) { 
			this.form.status=2;
            ordCheck(this.form).then(response => {
              this.msgSuccess("操作成功");
              this.open = false;
              this.getList();
            });
         
        }
      });
    },
    /** 拒绝按钮 */
    refuse() {
      this.$refs["form"].validate(valid => {
        if (valid) { 
			this.form.status=3;
            ordCheck(this.form).then(response => {
              this.msgSuccess("操作成功");
              this.open = false;
              this.getList();
            });
         
        }
      });
    }
  }
};
</script>
