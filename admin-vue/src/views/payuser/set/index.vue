<template>
	
	<el-card>
	  <el-tabs v-model="activeName">
	    <el-tab-pane label="基本信息" name="info">
	      <basic-info-form ref="basicInfo" :info="info" />
	    </el-tab-pane>
		
		<el-tab-pane label="数据源信息" name="datasoruce" v-if="info.isVip==2">
			 
			 <el-card  >
				 <el-form   :model="info"   label-width="100px">
						   <el-form-item label="数据源编码" prop="dataCode">
							 <el-input      v-model="info.dataCode" />
						   </el-form-item>
				 		 				
				  </el-form> 
				 <el-button style="margin-left: 200px;" type="primary" @click="updateDataSource">保存</el-button> 
				 	<div style="color: red;margin-top: 10px;">
				 					 提示：可去数据源--数据源配置 复制code
				 	</div>
			 </el-card>
		
		</el-tab-pane>
		
		<el-tab-pane label="链接信息" name="linkinfo" v-if="info.isVip==2"> 
		 <el-card  >
		 			 <el-form   :model="info"   label-width="100px">
		 				   <el-form-item label="链接地址" prop="domainUrl">
		 					 <el-input  type="textarea"    v-model="info.domainUrl" />
		 				   </el-form-item>
		 				
		 			  </el-form> 
		 			 <el-button style="margin-left: 200px;" type="primary" @click="updateDomainUrl">保存</el-button> 
		 			<div style="color: red;margin-top: 10px;">
		 							 说明：必须在正确的业务地址如：http://saas1.lgy0999.com
		 			</div>
					 
		 </el-card> 
		</el-tab-pane>
		
		<el-tab-pane label="初始业务管理员信息" name="ywgly" v-if="info.isVip==2"> 
		<el-form   :model="info"   label-width="100px" style="margin-top: 20px;"
		   v-if="info.domainUrl!=null">
			<el-form-item label="业务管理员" prop="adminName">
			<el-input style="width: 200px;"     v-model="info.adminName" />
			 <el-button style="margin-left: 20px;" v-if="1==2"   type="text" @click="addAdmin">去添加</el-button> 
		
			 <el-button style="margin-left: 20px;"   type="text" @click="toBizSys">登录业务系统</el-button> 
			</el-form-item>
			 <div style="color: red;">
				 说明：必须在有业务数据库后，再初始化业务管理员
			 </div>
		</el-form>
		</el-tab-pane>
		
		<el-tab-pane label="后台备注" name="adminRemark">
	 
		  <el-card  >
			 <el-form   :model="info"   label-width="100px">
				   <el-form-item label="后台备注" prop="adminRemark">
					 <el-input  type="textarea"    v-model="info.adminRemark" />
				   </el-form-item>
				
			  </el-form> 
			 <el-button style="margin-left: 200px;" type="primary" @click="updateRemark">保存</el-button> 
			
		  </el-card> 
		
		</el-tab-pane>
		 
		
		</el-tabs>
	</el-card>
</template>

<script>

	import { getUser, updateRemark,updateStatus,updateDataSource,updateDomainUrl } from "@/api/mall/user";
		import basicInfoForm from "./basicInfoForm";
	export default {
	  name: "userSet",
	  components: {
	   basicInfoForm,
	  },
	  data() {
	    return {
			id:0,
	      // 选中选项卡的 name
	      activeName: "info",
	      // 表格的高度
	      tableHeight: document.documentElement.scrollHeight - 245 + "px",
	 
	      // 表详细信息
	      info: {}
	    };
	  },
	  created() {
	        const id = this.$route.params && this.$route.params.tableId;
			if (id) {
				this.id=id;
			  // 获取表详细信息
			  getUser(id).then(res => { 
			    this.info = res.data;  
			  });
			  
		  }
			
	  },
	  methods: {
	      updateRemark(){
			  var form={};
			  form.id=this.id;
			  form.remark=this.info.adminRemark;
			  updateRemark(form).then(response => {
			    this.msgSuccess("修改成功"); 
			  });
		  },
		  updateDomainUrl(){
		  			  var form={};
		  			  form.id=this.id;
		  			  form.url=this.info.domainUrl;
		  			  updateDomainUrl(form).then(response => {
		  			    this.msgSuccess("设置成功"); 
		  			  });
		  },
		  updateDataSource(){
		  			  var form={};
		  			  form.id=this.id;
		  			  form.code=this.info.dataCode;
		  			  updateDataSource(form).then(res => { 
						  if(res.code==200){
							  this.msgSuccess(res.msg); 
						  }else{
							  this.msgError(res.msg);
						  }
		  			      
		  			  });
		  },addAdmin(){
			  var url=this.info.domainUrl+"/initAdmin"+"?dcode="+this.info.dataCode
			    window.open(url);
		  },toBizSys(){
			  var url=this.info.domainUrl+"?dcode="+this.info.dataCode
			    window.open(url);
		  }
	  },
	  mounted() {
	   
	  }
	};
</script>

<style>
</style>