<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="手机号" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入手机号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
     
      <el-form-item label="名称" prop="realName">
        <el-input
          v-model="queryParams.realName"
          placeholder="请输入名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      
        
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
        <el-option  label="全部" value=""  />
		  <el-option  label="正常" value="1"  /> 
		 <el-option  label="过期冻结" value="2"  /> 
        <el-option  label="黑名单" value="3"  />
      
        </el-select>
      </el-form-item>
      <el-form-item label="数据源" prop="dataCode">
        <el-input
          v-model="queryParams.dataCode"
          placeholder="请输入数据源编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
     
      <el-col :span="1.5"> 
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['mall:user:select']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="租户ID" align="center" prop="id" />
	     <el-table-column label="租户名称" align="center" prop="realName" />
      <el-table-column label="手机号" width="120" align="center" prop="mobile" />  
      <el-table-column label="vip标志" :formatter="vipFormat"  align="center" prop="isVip" />
	  <el-table-column label="注册时间" align="center" prop="createTime" width="150"> 
	  </el-table-column>
      <el-table-column label="开始日期" align="center" prop="vipStartTime" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.vipStartTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="过期日期" align="center" prop="vipEndTime" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.vipEndTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="vip人数" align="center" prop="vipNums" />
      <el-table-column label="使用人数" align="center" prop="curNums" />
	     <el-table-column label="模板标志"   :formatter="buyModelFormat"  align="center" prop="buyModel" />
      <el-table-column label="状态" :formatter="statusFormat"  align="center" prop="status" /> 
      <el-table-column label="操作" width="120" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          
          <el-button
            size="mini"
            type="text" 
			   icon="el-icon-edit"
				       v-hasPermi="['mall:user:set']"
          @click="handlePreview(scope.row)"
          >配置</el-button>
					<el-button
					   size="mini"
					   type="text" 
					icon="el-icon-view"
					 @click="handleDetail(scope.row)"
					 >详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
 
  </div>
</template>

<script>
import { listUser, getUser,   exportUser } from "@/api/mall/user";

export default {
  name: "User",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 租户表格数据
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,  
		  isVip: 2, 
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询租户列表 */
    getList() {
      this.loading = true;
      listUser(this.queryParams).then(response => {
        this.userList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
	//
	vipFormat(row, column) {
	   var status= row.isVip;
	     if(status==1){
		   return "普通用户";
	   }else if(status==2){
		   return "VIP用户";
	   } 
	},
	buyModelFormat(row, column) { 
	     if(row.buyModel==1){
		   return "所有模板";
	    } 
		var str="";
		if(row.buyModel1==1){
		   str+= "民事模板,";
		} 
		if(row.buyModel2==1){
		   str+= "刑事模板,";
		} 
		if(row.buyModel3==1){
		   str+= "行政模板,";
		} 
		if(row.buyModel4==1){
		   str+= "其他模板,";
		} 
		if(str==''){
			return "未购买";
		}else{
			return "已购买"+str;
		}
	   
	},
	
	//
	statusFormat(row, column) {
	   var status= row.status;
	     if(status==1){
		   return "正常";
	   }else if(status==3){
		   return "黑名单";
	   } else if(status==2){
		   return "过期冻结";
	   } 
	},
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        mobile: null,
        password: null,
        realName: null,
        address: null,
        isVip: null,
        vipStartTime: null,
        vipEndTime: null,
        vipNums: null,
        curNums: null,
        status: 0,
        createTime: null,
        updateTime: null,
        dataCode: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
   
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有租户数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportUser(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    },handlePreview(row) {
		const id = row.id || this.ids[0];
		this.$router.push("/payuser/set/" + id);
	},handleDetail(row) {
		const id = row.id || this.ids[0];
		this.$router.push("/payuser/detail/" + id);
	}
  }
};
</script>
