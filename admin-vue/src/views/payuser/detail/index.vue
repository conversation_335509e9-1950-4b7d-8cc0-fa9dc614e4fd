<template>
	
	<el-card>
	  <el-tabs v-model="activeName">
	    <el-tab-pane label="基本信息" name="info">
	      <basic-info-form ref="basicInfo" :info="info" />
	    </el-tab-pane>
		
		<el-tab-pane label="数据源信息" name="datasoruce">
			
			<el-card  v-if="info.dataCode==null" style="text-align: center;">
				   未配置
			 </el-card>
			 <el-card v-else>
				  编号： {{info.dataCode}}
			 </el-card>
		
		</el-tab-pane>
		
		<el-tab-pane label="链接信息" name="linkinfo">
		 <el-card  v-if="info.domainUrl==null" style="text-align: center;">
		 	   未配置
		  </el-card>
		  <el-card v-else>
		 	  地址： {{info.domainUrl}}
		  </el-card>
		</el-tab-pane>
		
		<el-tab-pane label="订单信息" name="ordinfo"    v-hasPermi="['mall:ord:select']">
		   <div>
			   
			   <el-table v-loading="loading" :data="ordList"  > 
			     <el-table-column label="订单号" align="center" prop="ordId" /> 
			     <el-table-column label="类型" align="center" prop="source" :formatter="sourceFormat" />
			     <el-table-column label="金额" align="center" prop="amount" />  
			   	 <el-table-column label="数量" align="center" prop="nums" :formatter="numsFormat" />
				  <el-table-column label="状态" align="center" prop="status"  :formatter="statusFormat"  />  
			     <el-table-column label="后台备注" align="center" prop="adminRemark" />
			    <el-table-column label="支付时间" align="center" prop="payTime" width="180"> 
			    </el-table-column> 
			   </el-table>
			   <pagination
			     v-show="total>0"
			     :total="total"
			     :page.sync="queryParams.pageNum"
			     :limit.sync="queryParams.pageSize"
			     @pagination="getList"
			   />
			   
			   
		   </div>
		</el-tab-pane>
		
		</el-tabs>
	</el-card>
</template>

<script>

	import { getUser } from "@/api/mall/user";
	import { listOrd } from "@/api/mall/ord";
		import basicInfoForm from "./basicInfoForm";
	export default {
	  name: "userDetail",
	  components: {
	   basicInfoForm,
	  },
	  data() {
	    return {
	      // 选中选项卡的 name
	      activeName: "info",
	      // 表格的高度
	      tableHeight: document.documentElement.scrollHeight - 245 + "px",
	 
	      // 表详细信息
	      info: {},
		  // 遮罩层
		  loading: true,
		  // 显示搜索条件
		  showSearch: false,
		  // 总条数
		  total: 0,
		  // 订单表格数据
		  ordList: [],
		  queryParams: {
		      pageNum: 1,
		      pageSize: 10,  
		    },
	    };
	  },
	  created() {
	        const id = this.$route.params && this.$route.params.tableId;
			if (id) {
			  // 获取表详细信息
			  getUser(id).then(res => { 
			    this.info = res.data; 
			  });
			  
			  this.queryParams.userId=id;
			  this.getList();
		  }
			
	  },
	  methods: {
	     getList() {
	       this.loading = true;
	       listOrd(this.queryParams).then(response => {
	         this.ordList = response.rows;
	         this.total = response.total;
	         this.loading = false;
	       });
	     },
	//
	statusFormat(row, column) {
	   var status= row.status;
	   if(status==0){
		   return "待支付"
	   }else if(status==1){
		   return "待审核";
	   }else if(status==2){
		   return "支付成功";
	   }else if(status==3){
		   return "已失败";
	   }else if(status==4){
		   return "支付中/未支付";
	   }
	},
	payWayFormat(row, column) {
	   var payWay= row.payWay;
	   if(payWay==1){
		   return "微信支付"
	   }else if(status==2){
		   return "支付宝";
	   }else if(status==3){
		   return "线下支付";
	   } 
	},
	sourceFormat(row, column) {
	   var source= row.source;
	   if(source==1){
		   return "购买vip"
	   }else if(source==2){
		   return "续费vip";
	   }else if(source==3){
		   return "购买人数";
	   }else if(source==4){
		   return "购买模板";
	   }else if(source==5){
		   return "购买vip服务";
	   }   
	},
	numsFormat(row, column) {
	   var source= row.source;
	   if(source==1){
		   return row.nums+"月"
	   }else if(source==2){
		  return row.nums+"月"
	   }else if(source==3){
		   return row.nums+"人"
	   }else if(source==4){
		  var nums= row.nums;
		  if(nums==1){
		  		   return "民事模板"
		  }else if(nums==2){
		  		   return "刑事模板";
		  }else if(nums==3){
		  		   return "行政模板";
		  }else if(nums==4){
		  		   return "其他模板";
		  } else if(nums==5){
		  		   return "所有模板";
		  }  
	   }else if(source==5){
		   var sms=row.months+"月_"+row.mans+"人_";
		    var nums= row.nums;
		    if(nums==1){
		    		   sms+="民事模板"
		    }else if(nums==2){
		    	  sms+="刑事模板"
		    }else if(nums==3){
		    	  sms+="行政模板"
		    }else if(nums==4){
		    	  sms+="其他模板"
		    } else if(nums==5){
		    	  sms+="所有模板"
		    } else if(nums==6){
				   sms+= "民事+刑事模板";
			   }  else if(nums==7){
				   sms+= "民事+行政模板";
			   } else if(nums==8){
				   sms+= "刑事+行政模板";
			   }   else if(nums==9){
				   sms+= "民事+民事+刑事模板";
			   }     
			return sms;
	   }   
	},
	  },
	  mounted() {
	   
	  }
	};
</script>

<style>
</style>