<template>
  <el-form ref="basicInfoForm" :model="info" :rules="rules" label-width="150px">
    <el-row>
      <el-col :span="8">
        <el-form-item label="手机号" prop="mobile">
          <el-input readonly="readonly"   v-model="info.mobile" />
        </el-form-item>
      </el-col>
	  <el-col :span="8">
	    <el-form-item label="状态" prop="status">
	      <el-input disabled v-model="info.status==1?'正常':'黑名单'" />
	    </el-form-item>
	  </el-col>
	  
	  <el-col :span="8"  >
	    <el-form-item label="VIP用户" prop="status">
	      <el-input disabled v-model="info.isVip==1?'否':'是'" />
	    </el-form-item>
	  </el-col>
	    
	  <el-col :span="8">
	    <el-form-item label="注册时间" prop="createTime">
	      <el-input disabled  v-model="info.createTime" />
	    </el-form-item>
	  </el-col>
	  
	  <el-col :span="8" v-if="info.checkStatus>0">
	    <el-form-item label="申请时间" prop="applyTime">
	      <el-input disabled  v-model="info.applyTime" />
	    </el-form-item>
	  </el-col>
	  
	  <el-col :span="8" v-if="info.checkStatus>0">
	    <el-form-item label="认证类型" prop="type">
	      <el-input  disabled v-model="info.type==1?'个人':'企业'" />
	    </el-form-item>
	  </el-col>
      <el-col :span="8" v-if="info.checkStatus>0">
        <el-form-item label="名称" prop="realName">
          <el-input disabled    v-model="info.realName" />
        </el-form-item>
      </el-col>
	  
	  <el-col :span="8" v-if="info.checkStatus>0">
	    <el-form-item label="证件号码" prop="no">
	      <el-input readonly="readonly"  v-model="info.no" />
	    </el-form-item>
	  </el-col>
	  
	  
	  <el-col :span="8"  v-if="info.checkStatus>0">
	    <el-form-item label="地址" prop="address">
	      <el-input disabled v-model="info.address" />
	    </el-form-item>
	  </el-col>
	  
	  <el-col :span="8" v-if="info.checkStatus>0">
	    <el-form-item label="审核状态" prop="checkStatus"> 
	      <el-input disabled  v-if="info.checkStatus==1" value="待审核" />
		 <el-input disabled  v-if="info.checkStatus==2" value="认证成功 " />
		  <el-input disabled  v-if="info.checkStatus==3" value="审核拒绝 " />
	    </el-form-item>
	  </el-col>
	  
	  <el-col :span="8" v-if="info.check_status>0">
	    <el-form-item label="审核时间" prop="checkTime">
	      <el-input disabled  v-model="info.checkTime" />
	    </el-form-item>
	  </el-col>
	  
	  
	  <el-col :span="8" v-if="info.check_status>0">
	    <el-form-item label="审核备注" prop="checkRemark">
	      <el-input disabled  v-model="info.checkRemark" />
	    </el-form-item>
	  </el-col>
	   
	  
	  <el-col :span="8" v-if="info.isVip==2">
	    <el-form-item label="开通时间" prop="vipStartTime">
	      <el-input disabled v-model="info.vipStartTime" />
	    </el-form-item>
	  </el-col>
	  
	  <el-col :span="8" v-if="info.isVip==2">
	    <el-form-item label="截止时间" prop="vipEndTime">
	      <el-input disabled v-model="info.vipEndTime" />
	    </el-form-item>
	  </el-col>
	  
	  <el-col :span="8" v-if="info.isVip==2">
	    <el-form-item label="vip人数" prop="vipNums">
	      <el-input disabled v-model="info.vipNums" />
	    </el-form-item>
	  </el-col>
	  
	  <el-col :span="8" v-if="info.isVip==2">
	    <el-form-item label="当前人数" prop="curNums">
	      <el-input disabled v-model="info.curNums" />
	    </el-form-item>
	  </el-col>
	
	  
	  <el-col :span="12">
	    <el-form-item label="后台备注" prop="adminRemark">
	      <el-input  type="textarea"  disabled v-model="info.adminRemark" />
	    </el-form-item>
	  </el-col>

     
 
	  
    
    </el-row>
  </el-form>
</template>
<script>
export default {
  name: "BasicInfoForm",
  props: {
    info: {
     type: Object,
     default: null
    }
  },
  data() {
    return {
      rules: {
         
      }
    };
  }
};
</script>
