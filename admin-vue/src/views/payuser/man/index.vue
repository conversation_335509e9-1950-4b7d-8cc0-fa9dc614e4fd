<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="手机号" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入手机号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> 
         
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
     
      <el-col :span="1.5"> 
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['mall:user:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange"> 
      <el-table-column label="手机号"  align="center" prop="mobile" />   
	  
	  <el-table-column label="注册时间" align="center" prop="createTime" > 
	  </el-table-column> 
	   <el-table-column label="后台备注"  align="center" prop="adminRemark" />   
	   <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
	     <template slot-scope="scope">
	       
	       <el-button
	         size="mini"
	         type="text" 
	      icon="el-icon-edit"
	       @click="updateRemark(scope.row)"
	       >修改备注</el-button>
	     </template>
	   </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
	
	
	
	
	    <!-- 修改备注对话框 -->
	    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
	      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
	        <el-form-item label="手机号" prop="mobile">
	          <el-input disabled v-model="form.mobile" placeholder="请输入手机号" />
	        </el-form-item>
	         
	        <el-form-item label="备注" prop="remark">
	          <el-input v-model="form.remark" placeholder="请输入备注" />
	        </el-form-item>
	      </el-form>
	      <div slot="footer" class="dialog-footer">
	        <el-button type="primary" @click="submitForm">确 定</el-button>
	        <el-button @click="cancel">取 消</el-button>
	      </div>
	    </el-dialog>
 
  </div>
</template>

<script>
import { listUser, getUser,updateRemark,  exportUser } from "@/api/mall/user";

export default {
  name: "User",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 租户表格数据
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        checkStatus: 0, 
        isVip: 1,
      
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询租户列表 */
    getList() {
      this.loading = true;
      listUser(this.queryParams).then(response => {
        this.userList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
	//
	vipFormat(row, column) {
	   var status= row.isVip;
	     if(status==1){
		   return "普通用户";
	   }else if(status==2){
		   return "VIP用户";
	   } 
	},
	//
	statusFormat(row, column) {
	   var status= row.status;
	     if(status==1){
		   return "正常";
	   }else if(status==3){
		   return "黑名单";
	   } else if(status==2){
		   return "过期冻结";
	   } 
	},
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        mobile: null,
        remark: null,
       
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    updateRemark(row) {
      this.reset();
      const id = row.id || this.ids
     this.form.remark=row.adminRemark;
     this.form.id=row.id;
	  this.form.mobile=row.mobile;
       this.open = true;
       this.title = "修改用户备注"; 
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {  
            updateRemark(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
         
        }
      });
    }
   ,
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有用户数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportUser(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    },handlePreview(row) {
		const id = row.id || this.ids[0];
		this.$router.push("/payuser/detail/" + id);
	}
  }
};
</script>
