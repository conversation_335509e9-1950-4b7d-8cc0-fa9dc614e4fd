<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="手机号" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入手机号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> 
			<el-form-item label="名称" prop="realName">
			  <el-input
			    v-model="queryParams.realName"
			    placeholder="请输入名称"
			    clearable
			    size="small"
			    @keyup.enter.native="handleQuery"
			  />
			</el-form-item> 
         
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
     
      <el-col :span="1.5"> 
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['mall:user:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange"> 
      <el-table-column label="手机号"  align="center" prop="mobile" />   
	  <el-table-column label="类型" :formatter="typeFormat"  align="center" prop="type" />   
	   <el-table-column label="名称"  align="center" prop="realName" />   
	  <el-table-column label="证件号码"  align="center" prop="no" /> 
	    <el-table-column label="地址"  align="center" prop="address" />   
			   <el-table-column label="申请时间"  align="center" prop="applyTime" />    
	   <el-table-column label="后台备注"  align="center" prop="adminRemark" />   
	   <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
	     <template slot-scope="scope">
	       
	       <el-button
	         size="mini"
	         type="text" 
	      icon="el-icon-edit"
	       @click="checkWin(scope.row)"
				  v-hasPermi="['mall:user:check']"
	       >审核</el-button>
	     </template>
	   </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
	
	
	
	
	    <!-- 审核对话框 -->
	    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
	      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
	        <el-form-item label="名称" prop="realName">
	          <el-input disabled v-model="form.realName"  />
	        </el-form-item>
			  
	        <el-form-item label="备注" prop="remark">
	          <el-input v-model="form.remark" placeholder="请输入审核备注" />
	        </el-form-item>
	      </el-form>
	      <div slot="footer" class="dialog-footer">
	        <el-button type="primary" @click="ok">通过</el-button>
			<el-button type="danger" @click="refuse">拒绝</el-button>
	        <el-button @click="cancel">取 消</el-button>
	      </div>
	    </el-dialog>
 
  </div>
</template>

<script>
import { listUser, getUser,checkUser,  exportUser } from "@/api/mall/user";

export default {
  name: "User",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 租户表格数据
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        checkStatus: 1,  
      
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询租户列表 */
    getList() {
      this.loading = true;
      listUser(this.queryParams).then(response => {
        this.userList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
	//
	typeFormat(row, column) {
	   var type= row.type;
	     if(type==1){
		   return "个人";
	   }else if(type==2){
		   return "企业";
	   } 
	}, 
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        remark: null, 
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 按钮操作 */
    checkWin(row) {
      this.reset();
      const id = row.id || this.ids
      this.form.realName=row.realName;
	  this.form.id=row.id;
        this.open = true;
        this.title = "租户认证审核"; 
    },
    /** 通过按钮 */
    ok() {
      this.$refs["form"].validate(valid => {
        if (valid) { 
			this.form.status=2;
            checkUser(this.form).then(response => {
              this.msgSuccess("操作成功");
              this.open = false;
              this.getList();
            });
         
        }
      });
    },
    /** 拒绝按钮 */
    refuse() {
      this.$refs["form"].validate(valid => {
        if (valid) { 
			this.form.status=3;
            checkUser(this.form).then(response => {
              this.msgSuccess("操作成功");
              this.open = false;
              this.getList();
            });
         
        }
      });
    }
   ,
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有用户数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportUser(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    } 
  }
};
</script>
