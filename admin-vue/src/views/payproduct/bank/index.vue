<template>
  <div class="app-container">
	  <el-form ref="form" :model="form" :rules="rules" label-width="80px">
	      <el-form-item label="名称" prop="name">
	        <el-input v-model="form.name" placeholder="请输入名称" />
	      </el-form-item>
	      <el-form-item label="卡号" prop="no">
	        <el-input v-model="form.no" placeholder="请输入卡号" />
	      </el-form-item>
	      <el-form-item label="开户行" prop="bank">
	        <el-input v-model="form.bank" placeholder="请输入开户行" />
	      </el-form-item>
	    </el-form>
	    <div slot="footer" class="dialog-footer" style="margin-left:200px;">
	      <el-button type="primary" @click="submitForm">确 定</el-button>
	      <el-button @click="cancel">取 消</el-button>
	    </div> 
	  
  </div>
</template>

<script>
import { listBank, getBank, delBank, addBank, updateBank, exportBank } from "@/api/mall/bank";

export default {
  name: "Bank",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 支付用户配置表格数据
      bankList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        no: null,
        bank: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
		  name: [
		    { required: true, message: "名称不能为空", trigger: "blur" }
		  ],
		  no: [
		    { required: true, message: "卡号不能为空", trigger: "blur" }
		  ],
		  bank: [
		    { required: true, message: "开户行不能为空", trigger: "blur" }
		  ],
      }
    };
  },
  created() { 
	  getBank(1).then(response => {
	    this.form = response.data; 
	  });
  },
  methods: { 
    
   
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          updateBank(this.form).then(response => {
            this.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
        }
      });
    }, 
  }
};
</script>
