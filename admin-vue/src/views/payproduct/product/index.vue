<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="产品名称" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入产品名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
     
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option  v-for="dict in statusOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"  />
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['mall:product:set']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['mall:product:set']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['mall:product:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['mall:product:select']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="productList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" /> 
	   <el-table-column label="编号" align="center" prop="id" />
      <el-table-column label="产品名称" align="center" prop="title" />
      <el-table-column label="原价(元)" align="center" prop="price" />
      <el-table-column label="售价(元)" align="center" prop="amount" />
      <el-table-column label="有效期(月)" align="center" prop="months" />  
	   <el-table-column prop="status" label="状态" :formatter="statusFormat" width="80"></el-table-column>
      <el-table-column label="推荐值" align="center" prop="recommend" />
	     <el-table-column label="备注" align="center" prop="remark" /> 
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['mall:product:set']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['mall:product:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改支付产品套餐对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="产品名称" prop="title">
          <el-input v-model="form.title" placeholder="请输入产品名称" />
        </el-form-item>
        <el-form-item label="原价(元)" prop="price">
          <el-input type="number" v-model="form.price" placeholder="请输入原价" />
        </el-form-item>
        <el-form-item label="售价(元)" prop="amount">
          <el-input type="number"  v-model="form.amount" placeholder="请输入售价" />
        </el-form-item>
        <el-form-item label="有效期" prop="months">
          <el-input v-model="form.months" placeholder="请输入有效期(月)" />
        </el-form-item>
     
        <el-form-item label="推荐值" prop="recommend">
          <el-input v-model="form.recommend" placeholder="请输入推荐值(越大越靠前)" />
        </el-form-item>
		<el-form-item label="备注" prop="remark">
		  <el-input v-model="form.remark" placeholder="请输入备注" />
		</el-form-item>
		<el-form-item label="状态">
		  <el-radio-group v-model="form.status">
		    <el-radio label="1">有效</el-radio>
			 <el-radio label="0">无效</el-radio>
		  </el-radio-group>
		</el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listProduct, getProduct, delProduct, addProduct, updateProduct, exportProduct } from "@/api/mall/product";

export default {
  name: "Product",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 支付产品套餐表格数据
      productList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        price: null,
        amount: null,
        months: null,
        status: null,
        nums: null,
        recommend: null
      },
      // 表单参数
      form: {},
	  // 状态数据字典
	  statusOptions: [ {
          value: "",
          label: "请选择"
        },{
          value: "1",
          label: "有效"
        },
        {
          value: "0",
          label: "无效"
        }],
      // 表单校验
      rules: {
				title: [
				  { required: true, message: "名称不能为空", trigger: "blur" }
				],
				price: [
				  { required: true, message: "原价不能为空", trigger: "blur" }
				],
				amount: [
				  { required: true, message: "售价不能为空", trigger: "blur" }
				],
				months: [
				  { required: true, message: "有效期不能为空", trigger: "blur" }
				], 
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询支付产品套餐列表 */
    getList() {
      this.loading = true;
      listProduct(this.queryParams).then(response => {
        this.productList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        price: null,
        amount: null,
        months: null,
        remark: null,
        status: 0,
        nums: null,
        createTime: null,
        updateTime: null,
        recommend: null
      };
      this.resetForm("form");
    },
    //  
    statusFormat(row, column) {
      return row.status == 1?'有效':'无效';
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加支付产品套餐";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getProduct(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改支付产品套餐";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateProduct(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addProduct(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除支付产品套餐编号为"' + ids + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delProduct(ids);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有支付产品套餐数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportProduct(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>
