<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="150px">
      <el-form-item label="初始人数" prop="nums">
        <el-input v-model="form.nums" placeholder="请输入VIP初始人数" />
      </el-form-item>
      <el-form-item label="单人价格(元/月)" prop="price">
        <el-input v-model="form.price" placeholder="请输入单人价格" />
      </el-form-item> 
   <el-form-item label="所有模板价格(元/月)" prop="modelPrice">
     <el-input v-model="form.modelPrice" placeholder="请输入所有模板价格" />
   </el-form-item> 
   <el-form-item label="所有模板描述" prop="buyModel1">
     <el-input v-model="form.buyModelRemark" placeholder="请输入所有模板描述" />
   </el-form-item> 
   <el-form-item label="民事模板价格(元/月)" prop="buyModel1">
     <el-input type="number" v-model="form.buyModel1" placeholder="请输入模板价格" />
   </el-form-item> 
   <el-form-item label="民事模板描述" prop="buyModel1Remark">
     <el-input v-model="form.buyModel1Remark" placeholder="请输入民事模板描述" />
   </el-form-item> 
   <el-form-item label="刑事模板价格(元/月)" prop="buyModel2">
     <el-input type="number" v-model="form.buyModel2" placeholder="请输入刑事模板价格" />
   </el-form-item> 
   <el-form-item label="刑事模板描述" prop="buyModel2Remark">
     <el-input v-model="form.buyModel2Remark" placeholder="请输入刑事模板描述" />
   </el-form-item> 
   <el-form-item label="行政模板价格(元/月)" prop="buyModel3">
     <el-input type="number" v-model="form.buyModel3" placeholder="请输入行政模板价格" />
   </el-form-item> 
   <el-form-item label="行政模板描述" prop="buyModel3Remark">
     <el-input v-model="form.buyModel3Remark" placeholder="请输入行政模板描述" />
   </el-form-item> 
   <el-form-item label="其他模板价格(元/月)" prop="buyModel4">
     <el-input type="number" v-model="form.buyModel4" placeholder="请输入其他模板价格" />
   </el-form-item> 
   <el-form-item label="其他模板描述" prop="buyModel4Remark">
     <el-input v-model="form.buyModel4Remark" placeholder="请输入其他模板描述" />
   </el-form-item> 
   
   <el-form-item label="民事+刑事模板价格(元/月)" prop="buyModel5">
     <el-input type="number" v-model="form.buyModel5" placeholder="请输入民事+刑事模板价格" />
   </el-form-item> 
   <el-form-item label="民事+刑事模板描述" prop="buyModel5Remark">
     <el-input v-model="form.buyModel5Remark" placeholder="请输入民事+刑事模板描述" />
   </el-form-item> 
   
   <el-form-item label="民事+行政模板价格(元/月)" prop="buyModel6">
     <el-input type="number" v-model="form.buyModel6" placeholder="请输入民事+行政模板价格" />
   </el-form-item> 
   <el-form-item label="民事+行政模板描述" prop="buyModel6Remark">
     <el-input v-model="form.buyModel6Remark" placeholder="请输入民事+行政模板描述" />
   </el-form-item> 
   
   <el-form-item label="刑事+行政模板价格(元/月)" prop="buyModel7">
     <el-input type="number" v-model="form.buyModel7" placeholder="请输入刑事+行政模板价格" />
   </el-form-item> 
   <el-form-item label="刑事+行政模板描述" prop="buyModel7Remark">
     <el-input v-model="form.buyModel7Remark" placeholder="请输入刑事+行政模板描述" />
   </el-form-item> 
      
  <el-form-item label="民事+行政+刑事模板价格(元/月)" prop="buyModel8">
	<el-input type="number" v-model="form.buyModel8" placeholder="请输入民事+行政+刑事模板价格" />
  </el-form-item> 
  <el-form-item label="民事+行政+刑事模板描述" prop="buyModel8Remark">
	<el-input v-model="form.buyModel8Remark" placeholder="请输入民事+行政+刑事模板描述" />
  </el-form-item> 
      
    </el-form>
    <div slot="footer" class="dialog-footer" style="margin-left:200px;">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </div>
</template>

<script>
import { listConfig, getConfig, delConfig, addConfig, updateConfig, exportConfig } from "@/api/mall/config";

export default {
  name: "Config",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 人数套餐配置表格数据
      configList: [],
      // 弹出层标题
      title: "",
 
      // 表单参数
      form: {
		  nums:'',
		  price:'',
		  modelPrice:'',
		  buyModel1:'',
		  buyModel2:'',
		  buyModel3:'',
		  buyModel4:'',
		  buyModelRemark:'',
		  buyModel1Remark:'',
		  buyModel2Remark:'',
		  buyModel3Remark:'',
		  buyModel4Remark:'',
		  buyModel5:'',
		  buyModel5Remark:'',
		  buyModel6:'',
		  buyModel6Remark:'',
		  buyModel7:'',
		  buyModel7Remark:'',
		  buyModel8:'',
		  buyModel8Remark:'',
	  },
      // 表单校验
      rules: {
      }
    };
  },
  created() {
   getConfig(1).then(response => {
     this.form = response.data;
     this.open = true;
     this.title = "修改支付配置";
   });
  },
  methods: {
     
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateConfig(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addConfig(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    } 
  }
};
</script>
