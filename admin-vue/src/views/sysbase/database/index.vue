<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="CODE" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入CODE"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="地址" prop="url">
        <el-input
          v-model="queryParams.url"
          placeholder="请输入数据源地址"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入数据库用户"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> 
			<el-form-item label="状态" prop="status">
			   <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
			   <el-option  label="全部" value=""  />
			 <el-option  label="空闲" value="1"  /> 
			<el-option  label="使用中" value="2"  />  
			 
			   </el-select>
			 </el-form-item>
    
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['mall:database:set']"
        >新增</el-button>
      </el-col>
      
      
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="databaseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
			   <el-table-column label="编号" align="center" prop="id" />
      <el-table-column label="CODE" align="center" prop="code" />
      <el-table-column label="地址" align="center" prop="url" />
      <el-table-column label="用户" align="center" prop="userName" />
      <el-table-column label="密码" align="center" key="userPwd" >
		    <template  slot-scope="scope">
				  ****
				  <el-button
				    size="mini"
				    type="text" 
				    @click="selPwd(scope.row)"
				    v-hasPermi="['mall:database:edit']"
				  >查看密码</el-button>
				</template>
		</el-table-column>
      <el-table-column label="租户ID" align="center" prop="payUserId" />
	   <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['mall:database:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['mall:database:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改数据库源对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">  
        <el-form-item label="地址" prop="url">
          <el-input v-model="form.url" placeholder="请输入数据库地址(ip:端口/数据库名称)" />
        </el-form-item>
        <el-form-item label="用户" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入数据库用户" />
        </el-form-item>
        <el-form-item label="密码" prop="userPwd">
          <el-input type="password" v-model="form.userPwd" placeholder="请输入数据库密码" />
        </el-form-item> 
		<el-form-item label="备注" prop="userPwd">
		  <el-input v-model="form.remark" placeholder="请输入备注" />
		</el-form-item> 
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDatabase, getDatabase, delDatabase, addDatabase, updateDatabase, exportDatabase } from "@/api/mall/database";

export default {
  name: "Database",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据库源表格数据
      databaseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        code: null,
        url: null,
        userName: null,
        userPwd: null,
        payUserId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询数据库源列表 */
    getList() {
      this.loading = true;
      listDatabase(this.queryParams).then(response => {
        this.databaseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        code: null,
        url: null,
        userName: null,
        userPwd: null,
        createTime: null,
        payUserId: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加数据库源";
    },selPwd(row){
		 this.msgSuccess(row.userPwd);
	},
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDatabase(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改数据库源";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDatabase(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDatabase(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除数据库源编号为"' + ids + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delDatabase(ids);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有数据库源数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportDatabase(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>
