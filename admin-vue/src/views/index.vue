<template>
  <div class="app-container home">
    
 </div>
 </template>

<style scoped lang="scss">
  .el-card.is-always-shadow {
    box-shadow: none !important;
  }

  .el-card {
    border-radius: 0px;
  }

  .clearfix{
    margin-top: 30px;
  }

  .clearfix-title {
    font-size: 24px;
    font-weight: 1000;
  }

  .clearfix-text {
    height: 90px;
    font-size: 14px;
    color: gray;
  }

  .clearfix-text-2 {
    font-size: 14px;
    color: gray;
  }

  .left-image {
    width: 320px;
    height: 310px;
    margin-top: 50px;
    margin-bottom: 120px;
  }

  .code {
    width: 156px;
    height: 156px;
    margin-bottom: 40px;
    display: flex;
    margin: auto;
  }

  .right-image {
    width: 720px;
    height: 360px;
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .right-image-2 {
    width: 680px;
  }

  .advertising {
    position: fixed;
    top: 130px;
    right: 30px;
  }

  .app-container {
    padding: 0;
  }

  .home {
    blockquote {
      padding: 10px 20px;
      margin: 0 0 20px;
      font-size: 17.5px;
      border-left: 5px solid #eee;
    }

    hr {
      margin-top: 20px;
      margin-bottom: 20px;
      border: 0;
      border-top: 1px solid #eee;
    }

    .col-item {
      margin-bottom: 20px;
    }

    ul {
      padding: 0;
      margin: 0;
    }

    font-family: "open sans",
    "Helvetica Neue",
    Helvetica,
    Arial,
    sans-serif;
    font-size: 13px;
    color: #676a6c;
    overflow-x: hidden;

    ul {
      list-style-type: none;
    }

    h4 {
      margin-top: 0px;
    }

    h2 {
      margin-top: 10px;
      font-size: 26px;
      font-weight: 100;
    }

    p {
      margin-top: 10px;

      b {
        font-weight: 700;
      }
    }

    .update-log {
      margin-top: 20px;

      ol {
        display: block;
        list-style-type: decimal;
        margin-block-start: 1em;
        margin-block-end: 1em;
        margin-inline-start: 0;
        margin-inline-end: 0;
        padding-inline-start: 40px;
      }
    }
  }
</style>
