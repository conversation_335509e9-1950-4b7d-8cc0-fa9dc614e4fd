// vue.config.js
const isProd = process.env.NODE_ENV === 'production'

const vueConfig = {
    css: {
        loaderOptions: {
            less: {
                modifyVars: {
                    // less vars，customize ant design theme

                    // 'primary-color': '#F5222D',
                    // 'link-color': '#F5222D',
                    'border-radius-base': '2px'
                },
                // DO NOT REMOVE THIS LINE
                javascriptEnabled: true
            }
        }
    },

    devServer: {
        // development server port 8000
        port: 8000,
        // If you want to turn on the proxy, please remove the mockjs /src/main.jsL11
        proxy: {
            '/law': {
                //target: 'https://saas1.lgy0999.com/',
                target: 'http://localhost:8081/',
                // target: 'http://law.vaiwan.com/',
                // target: 'http://law.ngrok.24k.fun/',
                changeOrigin: true,
            }
        }
    },

    // disable source map in production
    productionSourceMap: false,
    lintOnSave: false,
    // babel-loader no-ignore node_modules/*
    transpileDependencies: []
}
module.exports = vueConfig
